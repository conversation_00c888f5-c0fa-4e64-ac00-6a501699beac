import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import Components from 'unplugin-vue-components/vite';
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers';
import { resolve } from 'path';
import { injectVersionInfoPlugin } from './plugins/vite/vite-plugin-inject-version-info';
import { VantResolver } from '@vant/auto-import-resolver';

// https://vitejs.dev/config/
export default defineConfig({
  base: './',
  server: {
    open: true,
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@import "src/css/variables.scss";`,
      },
    },
  },
  plugins: [
    vue(),
    Components({
      resolvers: [NaiveUiResolver(), VantResolver()],
      directoryAsNamespace: true,
    }),
    injectVersionInfoPlugin(),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      src: resolve(__dirname, 'src'),
    },
  },
  build: {
    rollupOptions: {
      input: {
        h5: resolve(__dirname, 'h5/index.html'), // h5入口
        'pc-uc': resolve(__dirname, 'pc-uc/index.html'), // pc端用户中心入口
        'pc-portal': resolve(__dirname, 'pc-portal/index.html'), // pc端门户入口
      },
    },
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    cssCodeSplit: true,
  },
});

{"name": "qhy-sign-h5", "private": true, "version": "0.0.3", "type": "module", "scripts": {"dev": "vite --open /pc-uc/", "build": "vite build", "tsc": "vue-tsc --noEmit", "lint": "eslint . --ext .js,.ts,.vue --ignore-path .eslint<PERSON>ore", "lint:fix": "eslint . --ext .js,.ts,.vue --fix", "prepare": "husky install", "preview": "vite preview"}, "dependencies": {"@vueuse/core": "^10.9.0", "artplayer": "^5.1.7", "colord": "^2.9.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "easytimer.js": "^4.6.0", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "echarts-wordcloud": "^2.1.0", "flv.js": "^1.6.2", "hls.js": "^1.5.16", "js-file-downloader": "^1.1.25", "lodash-es": "^4.17.21", "nanoid": "^5.0.8", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.3", "quill": "^2.0.3", "rxjs": "^7.8.1", "sm-crypto": "^0.3.13", "vant": "^4.9.8", "vue": "3.6.0-alpha.2", "vue-router": "^4.3.2", "weixin-jsapi": "^1.1.0"}, "devDependencies": {"@commitlint/cli": "17.8.1", "@commitlint/config-angular": "17.8.1", "@kalimahapps/vue-icons": "^1.4.1", "@tanzerfe/eslint-config-lint": "^0.0.7", "@tanzerfe/http": "^1.0.1", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^20.12.10", "@types/querystringify": "^2.0.2", "@types/sm-crypto": "^0.3.4", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-vue": "^5.0.4", "autoprefixer": "^10.4.19", "husky": "8.0.3", "lint-staged": "13.3.0", "naive-ui": "^2.38.2", "postcss": "^8.4.38", "postcss-pxtorem": "6.1.0", "querystringify": "^2.2.0", "sass": "1.77.0", "svgson": "5.2.1", "tailwind-config": "^0.1.2", "tailwindcss": "^3.4.3", "terser": "^5.34.1", "typescript": "^5.5.4", "unplugin-vue-components": "^0.27.0", "vite": "^5.2.11", "vue-tsc": "^2.1.4"}, "husky": {"hooks": {"pre-commit": "lint-staged", "post-commit": "git update-index --again", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{vue,ts,tsx,js,jsx}": "eslint --ignore-path .eslint<PERSON>ore --fix"}}
/* 获取URL参数 */
(function(w){w.$_getUrlParams=function(name){var reg=new RegExp('(^|&)'+name+'=([^&]*)(&|$)');var r=location.href.substring(location.href.indexOf('?')).substr(1).match(reg);if(r!==null){return decodeURIComponent(r[2])}return null}})(window);
/* ScriptInjector */
(function(w){!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).ScriptInjector={})}(this,function(e){"use strict";function t(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function f(r){for(var e=1;e<arguments.length;e++){var o=null!=arguments[e]?arguments[e]:{};e%2?t(Object(o),!0).forEach(function(e){var t,n;t=r,n=o[e=e],e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(o)):t(Object(o)).forEach(function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(o,e))})}return r}var n=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t>>>0};e.inject=function(c,l){var e,a={jsInline:{tag:"script",attrs:{type:null!=(e=null==l||null==(e=l.attrs)?void 0:e.type)?e:"text/javascript"}},jsLink:{tag:"script",attrs:{type:null!=(e=null==l||null==(e=l.attrs)?void 0:e.type)?e:"text/javascript",src:c}},cssInline:{tag:"style",attrs:{}},cssLink:{tag:"link",attrs:{type:"text/css",rel:"stylesheet",href:c}}},s=(null==l?void 0:l.document)||document,u=(null==l?void 0:l.type)||"js",p="".concat(u,"-").concat((null==l?void 0:l.id)||String(n(c))),d=s.getElementById(p);return d&&null!=l&&l.shouldReplace&&(d.parentElement.removeChild(d),d=null),new Promise(function(e,n){var t,r=null!=(r=null==l?void 0:l.isLink)?r:/(^https?:)|(^\.{0,2}\/)/.test(c),o="".concat(u).concat(r?"Link":"Inline"),i=a[o];d?n(new Error("The ".concat(i.tag," element already exists."))):(d=s.createElement(i.tag),t=f(f({},null==l?void 0:l.attrs),i.attrs),Object.keys(t).forEach(function(e){d.setAttribute(e,String(t[e]))}),d.id=p,r?(d.onload=function(){return e(d)},d.onerror=function(e){var t=new Error("The ".concat(i.tag," element load failed."));Object.assign(t,{event:e}),n(t)}):d.innerHTML=c,("body"===((null==l?void 0:l.injectIn)||"head")?s.body:s.head).appendChild(d),r||e(d))})},Object.defineProperty(e,"__esModule",{value:!0})});w.$_ScriptInjector=ScriptInjector;})(window);

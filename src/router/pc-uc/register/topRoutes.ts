/**
 * 顶级路由(实例挂载时注册) - 路由表
 */
import Error404 from '@/views/error/ErrorPage.vue';
import Error403 from '@/views/error/ErrorPage403.vue';
import { RouteRecordRaw } from 'vue-router';
import MainLayoutPc from '@/layouts/MainLayoutPc.vue';

export const topRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'pcUcTopRoute',
    component: MainLayoutPc,
    children: [
      {
        path: '',
        name: 'pcUcIndex',
        component: () => import('@/views/pc-uc/index.vue'),
      },
    ],
  },
  {
    path: '/login',
    name: 'pcLogin',
    component: () => import('@/views/pc/login/index.vue'),
  },
  {
    path: '/403',
    name: 'ERROR_403',
    component: Error403,
    meta: { noAuth: true },
  },
];

export const ROUTE_NAME = {
  ERROR_404: 'ERROR_404',
};

export const ERROR_404_ROUTE = {
  path: '/:catchAll(.*)*',
  name: ROUTE_NAME.ERROR_404,
  component: Error404,
  meta: { title: '404' },
};

import type { Router } from 'vue-router';
import { MessageReactive } from 'naive-ui/es/message/src/MessageProvider';
import { sleep } from '@/utils/time.ts';
import useMndCtx from '@/common/shareContext/useMndCtx.ts';

export function setupRouterGuard(router: Router) {
  createPageGuard(router);
}

/**
 * 页面路由权限
 * @param router
 */
export function createPageGuard(router: Router) {
  let updaterLoadingInst: MessageReactive | undefined;
  let updaterTimer: unknown;

  router.beforeEach(async (to, from, next) => {
    if (window.$sysUpdater?.hasNewVer && !updaterLoadingInst) {
      // 开始 loadingBar
      useMndCtx().loadingBar.start();
      // 显示升级 loading
      updaterLoadingInst = useMndCtx().message.loading(window.$sysUpdater.updateVerTip, { duration: 0 });
    }

    next();
  });

  router.afterEach(() => {
    if (window.$sysUpdater?.hasNewVer && !updaterTimer) {
      document.title = window.$sysUpdater.updateVerTip;
      updaterTimer = setTimeout(async () => {
        // 结束 loadingBar
        useMndCtx().loadingBar.finish();
        await sleep(1000);
        window.location.reload();
        updaterLoadingInst?.destroy();
        updaterLoadingInst = undefined;
        updaterTimer = undefined;
      }, 5000);
    }
    // 设置document title: useTitle(to.meta.title);
  });
}

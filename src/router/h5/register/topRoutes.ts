/**
 * 顶级路由(实例挂载时注册) - 路由表
 */
import MainLayoutH5 from '@/layouts/MainLayoutH5.vue';
import Error404 from '@/views/error/ErrorPage.vue';
import Error403 from '@/views/error/ErrorPage403.vue';
import { RouteRecordRaw } from 'vue-router';

export const topRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'topRoute',
    component: MainLayoutH5,
    children: [
      {
        path: '',
        name: 'homeIndex',
        component: () => import('@/views/home/<USER>'),
      },
      {
        path: '/application-form',
        name: 'applicationForm',
        component: () => import('@/views/application/form.vue'),
        meta: { title: '提交报名信息' },
      },
      {
        path: '/application-detail',
        name: 'applicationDetail',
        component: () => import('@/views/application/detail.vue'),
        meta: { title: '查看报名信息' },
      },
      {
        path: 'pay',
        name: 'payPay',
        component: () => import('@/views/pay/Pay.vue'),
        meta: { title: '支付' },
      },
      {
        path: 'pay-jump',
        name: 'payJump',
        component: () => import('@/views/pay/PayJump.vue'),
        meta: { title: '正在支付' },
      },
      {
        path: 'pay-check',
        name: 'payCheck',
        component: () => import('@/views/pay/PayCheck.vue'),
        meta: { title: '正在获取状态' },
      },
      {
        path: '/order',
        name: 'order',
        component: () => import('@/views/order/index.vue'),
        meta: { title: '报名' },
      },
      {
        path: '/workbenches',
        name: 'workbenches',
        component: () => import('@/views/workbenches/index.vue'),
        meta: { title: '工作台' },
      },
      {
        path: '/hotel-submission',
        name: 'hotelSubmission',
        component: () => import('@/views/hotel/form.vue'),
        meta: { title: '提交行程信息', noAuth: true },
      },
      {
        path: '/hotel-success',
        name: 'hotelSuccess',
        component: () => import('@/views/hotel/success.vue'),
        meta: { title: '提交成功', noAuth: true },
      },
    ],
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
    meta: { noAuth: true },
  },
  {
    path: '/403',
    name: 'ERROR_403',
    component: Error403,
    meta: { noAuth: true },
  },
  {
    path: '/to-sign',
    name: 'toSign',
    component: () => import('@/views/sign/index.vue'),
    meta: { noAuth: true },
  },
];

export const ROUTE_NAME = {
  ERROR_404: 'ERROR_404',
};

export const ERROR_404_ROUTE = {
  path: '/:catchAll(.*)*',
  name: ROUTE_NAME.ERROR_404,
  component: Error404,
  meta: { title: '404' },
};

/**
 * 公共配色
 * 命名规则: 以 skin-c 开头
 */

// 转换十六进制颜色值为 RGB 字符串, 转换后的值支持设置透明度
@function hex-to-rgb($hex) {
    @return red($hex), green($hex), blue($hex);
}


:root {
    // primary
    $color-primary: #008982;

    // com
    --skin-c1: #008982;
    --skin-c2: #20a32d;
    --skin-c3: #ff3232;
    --skin-c4: #00fefe;
    --skin-c5: #093c6c;
    --skin-c6: #0c87e5;

    // 以下定义的 -rgb 值 仅供 plugin-custom-opacity 插件使用
    --skin-c1-rgb: #{hex-to-rgb(#008982)};
    --skin-c2-rgb: #{hex-to-rgb(#20a32d)};
    --skin-c3-rgb: #{hex-to-rgb(#ff3232)};
    --skin-c4-rgb: #{hex-to-rgb(#00fefe)};
    --skin-c5-rgb: #{hex-to-rgb(#093c6c)};
    --skin-c6-rgb: #{hex-to-rgb(#0c87e5)};
}

import { stringify } from 'querystringify';
import { merge } from 'lodash-es';
// api file
import zyx from './zyx';
import gj from './gj';
import hy from './hy';
import lc from './lc';
import zyl from './zyl';
import jzy from './jzy';
import sct from './sct';

export const api = {
  type: {
    nil: '',
    sus: import.meta.env.DEV ? 'sign-up-service-test' : 'sign-up-service',
    dcs: '', // 管理后台服务
    portal: '', /// 门户网站服务
  },
  name: merge(zyx, hy, gj, lc, zyl, jzy, sct),

  /**
   * 组装请求地址
   * @param serviceType
   * @param apiName
   * @param query
   */
  getUrl(serviceType: string, apiName: string, query?: any): string {
    const url = window.$SYS_CFG.apiBaseURL;
    const paramsStr = query ? `?${stringify(query)}` : '';
    const _apiName = apiName?.indexOf('/') === 0 ? apiName : '/' + apiName;
    const _serviceType = serviceType ? '/' + serviceType : '';

    return `${url}${_serviceType}${_apiName}${paramsStr}`;
  },
};

<template>
  <div :class="$style['main-layout']">
    <Header :class="$style['header']" />
    <router-view :class="$style['content']" />
  </div>
</template>

<script lang="ts" setup>
import Header from '@/views/header/index.vue';

defineOptions({ name: 'MainLayoutH5' });
</script>

<style module lang="scss">
.main-layout {
  --header-height: 42px;

  height: 100vh;
  height: 100svh;
  display: grid;
  grid-template-rows: var(--header-height) calc(100vh - var(--header-height));
}

.header {
  height: var(--header-height);
  line-height: var(--header-height);
}

.content {
  overflow: auto;
  padding: 0 24px 24px;
}
</style>

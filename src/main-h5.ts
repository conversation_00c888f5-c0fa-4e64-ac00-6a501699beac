import { setupApi, setupAssets, setupStore } from '@/plugins';
import { createApp } from 'vue';
import App from './App.vue';
import router from '@/router/h5';

async function setupApp() {
  const app = createApp(App);

  // import assets: js、css
  setupAssets();

  // register store
  setupStore(app);

  // api
  setupApi();

  // register module router
  app.use(router);

  app.mount('#app');
}

setupApp().then();

export interface IUpload {
  id: string;
  name: string;
  percentage: number;
  status: 'pending' | 'uploading' | 'error' | 'finished' | 'removed';
  url: null | string;
  file: File;
  thumbnailUrl: null | string;
  type: string;
  fullPath: string;
  batchId: string;
  res: IUploadRes | null;
}

export interface IUploadRes {
  businessData: string;
  businessId: string;
  businessType: string;
  fileName: string;
  filePath: string;
  fileSize: string;
  id: string;
  isDel: string;
  suffix: string;
  uploadDate: string;
  videoTime: string;
}

<template>
  <div class="menu-page">
    <div class="menu-btn" v-for="(item, i) in menuList" :key="i" @click="handleMenuBtn(item.routerName)">
      <div class="btn-lf">{{ item.title }}</div>
      <div class="btn-rg">
        <img :src="navIcons[item.img]" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useMetaGlobEager } from '@/common/hooks/useMetaGlobEager';
import { useRouter } from 'vue-router';

interface IMenuList {
  title: string;
  img: string;
  routerName: string;
}

interface Props {
  menuList: IMenuList[];
}

const props = withDefaults(defineProps<Props>(), {
  menuList: () => [],
});

const router = useRouter();

const iconsDir = import.meta.glob('./assets/*.png', { eager: true });
const navIcons = useMetaGlobEager(iconsDir);

function handleMenuBtn(routerName: string) {
  router.push({ name: routerName });
}

defineOptions({ name: 'MenuPage' });
</script>

<style scoped lang="scss">
.menu-page {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(4, 59px);
  grid-gap: 10px 14px;

  .menu-btn {
    width: 100%;
    height: 100%;
    background: rgba(210, 225, 253, 0.31);
    border-radius: 10px 10px 10px 10px;
    padding: 9px 7px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .btn-lf {
      flex: 1;
      font-weight: 600;
      font-size: 15px;
      color: #000000;
      margin-right: 5px;
    }
    .btn-rg {
      width: 34px;
      height: 34px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>

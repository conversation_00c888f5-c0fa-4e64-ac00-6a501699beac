<template>
  <div class="absolute top-0 left-0 w-full h-full z-10 bg-white/90 dark:(bg-black/80)" v-if="uploading">
    <n-spin :show="uploading" class="w-full h-full"></n-spin>
    <n-progress
      class="item-progress"
      type="line"
      :percentage="percentage"
      :stroke-width="10"
      color="var(--skin-c1)"
      processing
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';

// isEnd 为true 时模拟上传完成 100%
const props = defineProps({ uploading: { type: Boolean, default: false }, isEnd: { type: Boolean, default: false } });
const percentage = ref(0);
const timer = ref();
watch(
  () => [props.uploading, props.isEnd],
  ([val1, val2]) => {
    if (val1) {
      percentage.value = val2 ? 100 : 0;
      // 模拟上传进度条
      timer.value = setInterval(() => {
        if (percentage.value <= 90) percentage.value += (Math.random() * 10) >> 0;
      }, 200);
    } else {
      clearInterval(timer.value);
      percentage.value = 0;
    }
  }
);

defineOptions({ name: 'FileImportComp' });
</script>

<style scoped lang="scss">
.item-progress {
  width: 50%;
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2001;
}
</style>

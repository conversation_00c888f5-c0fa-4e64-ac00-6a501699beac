<!--柱状图-->
<template>
  <div v-if="!isEmpty" class="w-full h-full" ref="barChartRef"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import { defineComponent, markRaw, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import Empty from '@/components/empty/index.vue';
import { sleep } from '@/utils/time';
import * as echarts from 'echarts';

defineComponent({ name: 'doughnutChartCustom' });

const props = defineProps({
  echartsData: {
    type: Array,
    default: () => {
      return [];
    },
  },
  color: {
    type: Array,
    default: () => {
      return [
        ['rgba(2, 94, 112, 1)', 'rgba(30, 203, 237, 1)'],
        ['rgba(117, 81, 3, 1)', 'rgba(219, 185, 25, 1)'],
      ];
    },
  },
  showLabel: {
    type: Boolean,
    default: true,
  },
  showRing: {
    type: Boolean,
    default: true,
  },
  borderColor: {
    type: Array,
    default: () => {
      return ['rgba(96, 179, 213, 1)', 'rgba(18, 59, 76, 0.7)', 'rgba(96, 179, 213, 1)'];
    },
  },
  extra: {
    type: Object,
    default: () => {
      return { yAxis: {}, legend: {}, tooltip: {}, grid: {}, title: {} };
    },
  },
});

const isEmpty = ref(false);
const barChartRef = ref(null);
const myChart = ref<any>(null);

function initEcharts(data: any[]) {
  if (myChart.value) destroyEcharts();
  // 标记一个对象，使其永远不会再成为响应式对象
  const color: any[] = props.color || [];
  myChart.value = markRaw(echarts.init(barChartRef.value));
  const option = {
    title: [...props.extra.title],
    xAxis: {
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
      },
      ...props.extra.xAxis,
    },
    grid: {
      top: '10%',
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
      ...props.extra.grid,
    },
    yAxis: {
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#999',
      },
    },
    tooltip: {
      trigger: 'axis',
      ...props.extra.tooltip,
    },
    series: [
      {
        type: 'bar',
        // showBackground: true,
        barWidth: 20,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' },
          ]),
        },
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2378f7' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#83bff6' },
            ]),
          },
        },
        data: data,
      },
    ],
  };
  myChart.value.setOption(option);
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any[]) => {
      destroyEcharts();
      // 空数组或数组内的value全为0时展示缺省
      isEmpty.value = !val.length;
      // isEmpty.value = !val.length || val.every((item) => item.value === 0);
      await sleep(500);
      if (!isEmpty.value && barChartRef.value) initEcharts(val);
    },
    { immediate: true }
  );
});

onBeforeUnmount(() => {
  destroyEcharts();
});
</script>

<style scoped></style>

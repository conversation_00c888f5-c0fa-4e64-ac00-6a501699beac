<!--环形图-->
<template>
  <div v-if="!isEmpty" class="normal-pie w-full h-full" ref="doughnutChart"></div>
  <Empty v-else />
</template>

<script lang="ts" setup>
import Empty from '@/components/empty/index.vue';
import { sleep } from '@/utils/time';
import * as echarts from 'echarts';
import { defineComponent, markRaw, onBeforeUnmount, onMounted, ref, watch } from 'vue';

defineComponent({ name: 'doughnutChartCustom' });

const props = defineProps({
  echartsData: {
    type: Array,
    default: () => {
      return [];
    },
  },
  color: {
    type: Array,
    default: () => {
      return [
        ['rgba(2, 94, 112, 1)', 'rgba(30, 203, 237, 1)'],
        ['rgba(117, 81, 3, 1)', 'rgba(219, 185, 25, 1)'],
      ];
    },
  },
  showLabel: {
    type: Boolean,
    default: true,
  },
  showRing: {
    type: Boolean,
    default: true,
  },
  borderColor: {
    type: Array,
    default: () => {
      return ['rgba(96, 179, 213, 1)', 'rgba(18, 59, 76, 0.7)', 'rgba(96, 179, 213, 1)'];
    },
  },
  extra: {
    type: Object,
    default: () => {
      return { yAxis: {}, legend: {}, tooltip: {}, grid: {}, title: {} };
    },
  },
});

const isEmpty = ref(false);
const doughnutChart = ref();
const myChart = ref<any>(null);

function initEcharts(data: any[]) {
  if (myChart.value) destroyEcharts();
  // 标记一个对象，使其永远不会再成为响应式对象
  const color: any[] = props.color || [];
  const showLabel = props.showLabel;
  const borderColor: any[] = props.borderColor || [];
  myChart.value = markRaw(echarts.init(doughnutChart.value));
  data.forEach((item, index) => {
    item.itemStyle = {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: color[index][0],
        },
        {
          offset: 1,
          color: color[index][1],
        },
      ]),
    };
  });
  const option = {
    title: props.extra?.title || {
      text: '',
      left: 'center',
      top: 20,
      textStyle: {
        color: '#333333',
      },
    },
    legend: {
      icon: 'circle',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 30,
      right: '30px',
      top: 'center',
      textStyle: {
        color: '#303133', // 图例文字颜色
        fontSize: 16,
      },
      padding: [14, 14],
      backgroundColor: '#F2F5FA',
      orient: 'vartical',
      formatter: (name: any) => {
        if (data.length) {
          const item = data.filter((item) => item.name === name)[0];
          return `${name}  ${item.value}  ${item.per}`;
        }
      },
      ...props.extra.legend,
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {c} ({d}%)',
      backgroundColor: 'RGBA(13, 29, 51, 0.8)',
      textStyle: {
        color: '#fff',
      },
    },
    series: [
      {
        name: '',
        type: 'pie',
        startAngle: 220,
        radius: ['15%', '55%'],
        center: ['50%', '40%'],
        data,
        roseType: 'radius',
        label: {
          normal: {
            show: showLabel,
            formatter: '{d}%',
            color: '#fff',
            fontFamily: 'D-DIN-PRO',
            fontSize: 16,
          },
        },
        labelLine: {
          normal: {
            show: showLabel,
            lineStyle: {
              color: '#fff',
            },
            smooth: 0.2,
            length: 15,
            length2: 10,
          },
        },
        ...props.extra.series1,
      },
    ],
  };
  if (props.showRing) {
    option.series.push({
      name: '',
      type: 'pie',
      startAngle: 90,
      radius: ['60%', '62%'],
      center: ['50%', '40%'],
      label: { show: false },
      labelLine: { show: false },
      emphasis: { disabled: true },
      tooltip: { show: false },
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0.05, color: borderColor[0] },
          { offset: 0.5, color: borderColor[1] },
          { offset: 1, color: borderColor[2] },
        ]),
      },
      data: [{ value: 100 }],
      ...props.extra.series2,
    });
  }

  myChart.value.setOption(option);
}

function destroyEcharts() {
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
}

onMounted(() => {
  watch(
    () => props.echartsData,
    async (val: any[]) => {
      destroyEcharts();
      // 空数组或数组内的value全为0时展示缺省
      isEmpty.value = !val.length || val.every((item) => item.value === 0);
      await sleep(500);
      if (!isEmpty.value && doughnutChart.value) initEcharts(val);
    },
    { immediate: true }
  );
});

onBeforeUnmount(() => {
  destroyEcharts();
});
</script>

<style scoped></style>

<template>
  <div :class="editorClasses" :style="editorStyles">
    <div ref="editorRef"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick, computed } from 'vue';
import Quill from 'quill';
import 'quill/dist/quill.snow.css';
import './quill.snow.ZH_CN.css';
import { api } from '@/api';
import { IUploadRes } from '@/components/upload/type';
import { $toast } from '@/common/shareContext';

// Props 定义
interface Props {
  modelValue?: string;
  placeholder?: string;
  theme?: 'snow' | 'bubble';
  readOnly?: boolean;
  toolbar?: any[] | string | boolean;
  modules?: Record<string, any>;
  formats?: string[];
  bounds?: string | HTMLElement;
  scrollingContainer?: string | HTMLElement;
  // 图片上传相关
  enableImageUpload?: boolean;
  imageUploadSize?: number; // MB
  imageUploadAccept?: string;
  // 高度相关
  height?: string | number;
  minHeight?: string | number;
  maxHeight?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  theme: 'snow',
  readOnly: false,
  toolbar: () => [
    [{ header: [1, 2, 3, false] }],
    ['bold', 'italic', 'underline'],
    [{ list: 'ordered' }, { list: 'bullet' }, { indent: '-1' }, { indent: '+1' }, { align: [] }],
    ['link', 'image', 'code-block', 'clean'],
  ],
  enableImageUpload: true,
  imageUploadSize: 10,
  imageUploadAccept: '.jpg,.jpeg,.png,.webp,.gif,.bmp,.svg',
  height: undefined,
  minHeight: 200,
  maxHeight: '60vh',
});

// Emits 定义
interface Emits {
  (e: 'update:modelValue', value: string): void;
  (e: 'change', value: string): void;
  (e: 'blur', quill: Quill): void;
  (e: 'focus', quill: Quill): void;
  (e: 'ready', quill: Quill): void;
  (e: 'image-upload-start'): void;
  (e: 'image-upload-success', data: IUploadRes): void;
  (e: 'image-upload-error', error: any): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const editorRef = ref<HTMLElement>();
let quillInstance: Quill | null = null;
const isUpdatingContent = ref(false);

// 高度相关计算属性
const editorClasses = computed(() => {
  const classes = ['quill-editor'];

  if (props.height === 'adaptive') {
    classes.push('height-adaptive');
  } else if (props.height && props.height !== 'auto') {
    classes.push('height-fixed');
  } else if (props.height === 'auto') {
    classes.push('height-auto');
  }

  return classes;
});

const editorStyles = computed(() => {
  const styles: Record<string, string> = {};

  // 处理固定高度
  if (props.height) {
    styles['--editor-height'] = typeof props.height === 'number' ? `${props.height}px` : props.height;
  }

  // 处理最小高度
  if (props.minHeight) {
    styles['--editor-min-height'] = typeof props.minHeight === 'number' ? `${props.minHeight}px` : props.minHeight;
  }

  // 处理最大高度
  if (props.maxHeight) {
    styles['--editor-max-height'] = typeof props.maxHeight === 'number' ? `${props.maxHeight}px` : props.maxHeight;
  }

  return styles;
});

// 初始化编辑器
const initQuill = async () => {
  if (!editorRef.value) return;

  await nextTick();

  const options = {
    theme: props.theme,
    placeholder: props.placeholder,
    readOnly: props.readOnly,
    modules: props.modules || {
      toolbar: props.toolbar,
    },
    formats: props.formats,
    bounds: props.bounds,
    scrollingContainer: props.scrollingContainer,
  };

  quillInstance = new Quill(editorRef.value, options);

  // 设置初始内容
  if (props.modelValue) {
    quillInstance.root.innerHTML = props.modelValue;
  }

  // 设置自定义图片上传处理器
  if (props.enableImageUpload) {
    const toolbar = quillInstance.getModule('toolbar') as any;
    toolbar.addHandler('image', handleImageUpload);
  }

  // 监听内容变化
  quillInstance.on('text-change', () => {
    if (!isUpdatingContent.value) {
      const html = quillInstance!.root.innerHTML;
      // 如果内容为空（只有空的p标签），返回空字符串
      const cleanHtml = html === '<p><br></p>' ? '' : html;
      emit('update:modelValue', cleanHtml);
      emit('change', cleanHtml);
    }
  });

  // 监听焦点事件
  quillInstance.on('selection-change', (range) => {
    if (range) {
      emit('focus', quillInstance!);
    } else {
      emit('blur', quillInstance!);
    }
  });

  // 编辑器准备就绪
  emit('ready', quillInstance);
};

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (quillInstance && !isUpdatingContent.value) {
      isUpdatingContent.value = true;
      const currentContent = quillInstance.root.innerHTML;
      if (currentContent !== newValue) {
        quillInstance.root.innerHTML = newValue || '';
      }
      isUpdatingContent.value = false;
    }
  }
);

// 监听只读状态变化
watch(
  () => props.readOnly,
  (newValue) => {
    if (quillInstance) {
      quillInstance.enable(!newValue);
    }
  }
);

// 图片上传相关函数
const handleImageUpload = () => {
  const input = document.createElement('input');
  input.setAttribute('type', 'file');
  input.setAttribute('accept', props.imageUploadAccept);
  input.click();

  input.onchange = () => {
    const file = input.files?.[0];
    if (file) {
      uploadImage(file);
    }
  };
};

const validateImageFile = (file: File): boolean => {
  // 检查文件类型
  const fileExt = file.name.slice(file.name.lastIndexOf('.') + 1).toLowerCase();
  const acceptedExts = props.imageUploadAccept.replace(/\./g, '').split(',');
  if (!acceptedExts.includes(fileExt)) {
    $toast.error(`请上传 ${props.imageUploadAccept} 类型的图片!`);
    return false;
  }

  // 检查文件大小
  if (props.imageUploadSize && file.size / 1024 / 1024 > props.imageUploadSize) {
    $toast.error(`图片不能超过 ${props.imageUploadSize} MB，请重新上传!`);
    return false;
  }

  return true;
};

const uploadImage = async (file: File) => {
  if (!validateImageFile(file)) {
    return;
  }

  emit('image-upload-start');

  try {
    const formData = new FormData();
    formData.append('file', file);

    const uploadUrl = api.getUrl(api.type.dcs, api.name.dcs.fileUpload);

    const response = await fetch(uploadUrl, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`上传失败: ${response.statusText}`);
    }

    const result = await response.json();
    const uploadData: IUploadRes = result.data;

    if (uploadData && uploadData.filePath) {
      // 构建图片完整 URL
      const imageUrl = window.$SYS_CFG.apiBaseFile + uploadData.filePath;

      // 插入图片到编辑器
      if (quillInstance) {
        const range = quillInstance.getSelection(true);
        quillInstance.insertEmbed(range.index, 'image', imageUrl);
        quillInstance.setSelection(range.index + 1);
      }

      emit('image-upload-success', uploadData);
      $toast.success('图片上传成功');
    } else {
      throw new Error('上传响应数据格式错误');
    }
  } catch (error) {
    console.error('图片上传失败:', error);
    emit('image-upload-error', error);
    $toast.error('图片上传失败，请重试');
  }
};

// 暴露方法给父组件
const getQuill = () => quillInstance;
const getHTML = () => {
  const html = quillInstance?.root.innerHTML || '';
  // 如果内容为空（只有空的p标签），返回空字符串
  return html === '<p><br></p>' ? '' : html;
};
const getText = () => quillInstance?.getText() || '';
const getLength = () => quillInstance?.getLength() || 0;
const focus = () => quillInstance?.focus();
const blur = () => quillInstance?.blur();
const insertImage = (imageUrl: string) => {
  if (quillInstance) {
    const range = quillInstance.getSelection(true);
    quillInstance.insertEmbed(range.index, 'image', imageUrl);
    quillInstance.setSelection(range.index + 1);
  }
};

defineExpose({
  getQuill,
  getHTML,
  getText,
  getLength,
  focus,
  blur,
  insertImage,
});

// 生命周期
onMounted(() => {
  initQuill();
});

onBeforeUnmount(() => {
  if (quillInstance) {
    quillInstance = null;
  }
});

defineOptions({ name: 'ComQuillEditor' });
</script>

<style lang="scss">
.quill-editor {
  width: 100%;

  .ql-editor {
    height: var(--editor-height, none);
    min-height: var(--editor-min-height, none);
    max-height: var(--editor-max-height, none);
  }
}
</style>

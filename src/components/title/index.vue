<template>
  <div class="title">{{ title }}</div>
</template>

<script setup lang="ts">
interface Props {
  title: string;
}
const props = defineProps<Props>();
defineOptions({ name: 'TitleComp' });
</script>

<style scoped lang="scss">
.title {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  color: #242526;
  border-bottom: 1px solid #ebeef5;
  padding: 0 24px;
  &::before {
    content: '';
    width: 3px;
    height: 20px;
    background: #008982;
    border-radius: 2px 2px 2px 2px;
    margin-right: 8px;
  }
}
</style>

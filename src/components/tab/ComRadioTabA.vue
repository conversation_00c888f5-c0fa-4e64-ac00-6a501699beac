<template>
  <div :class="$style['radio-tab']">
    <n-tabs :default-value="tab" @update:value="handleUpdateValue" size="large">
      <n-tab-pane v-for="tab in tabList" :key="tab.name" :name="tab.name" :tab="tab.label"></n-tab-pane>
    </n-tabs>
  </div>
</template>

<script setup lang="ts">
import type { ITabItem } from './type';

interface Props {
  tab: string;
  tabList: ITabItem[];
}

const props = defineProps<Props>();
const emits = defineEmits(['change']);

function handleUpdateValue(name: string) {
  const data = props.tabList.find((v) => v.name === name);
  emits('change', name, data);
}

defineOptions({ name: 'ComRadioTabA' });
</script>

<style module lang="scss">
.radio-tab {
  @apply bg-[#F4F9FF] h-[48px] border-b-[1px] border-[#EBEEF5] rounded-t-[4px];

  :global(.n-tabs) {
    @apply pl-[36px];
  }
  :global(.n-tabs-nav-scroll-content) {
    @apply h-[48px];
  }
  :global(.n-tab-pane) {
    display: none;
  }
}
</style>

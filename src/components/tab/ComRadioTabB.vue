<template>
  <n-tabs :default-value="value" @update:value="tabChange" size="large">
    <n-tab v-for="tab in tabs" :key="tab.key" :name="tab.key" :tab="tab.label" />
  </n-tabs>
</template>

<script lang="ts" setup>
import { useCssModule } from 'vue';

interface ITab {
  label: string;
  key: string;
}
interface Props {
  value: string;
  tabs: ITab[];
}
const props = withDefaults(defineProps<Props>(), {
  value: '',
  tabs: () => [],
});
const emits = defineEmits(['update:value', 'change']);
const style = useCssModule();

const tabChange = (key: string) => {
  emits('change', key);
  key !== props.value && emits('update:value', key);
};

defineOptions({ name: 'ComTabBComp' });
</script>

<style module lang="scss"></style>

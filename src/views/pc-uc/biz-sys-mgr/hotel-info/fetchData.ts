import { api } from '@/api';
import { IObj } from '@/types';
import { $http } from '@tanzerfe/http';
import { fileDownloader } from '@/views/pc/common/response';

export function getUserHotelPage(query: IObj<any>) {
  const url = api.getUrl(api.type.sus, api.name.sus.getUserHotelPage, query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}

export function getDiningInfo() {
  const url = api.getUrl(api.type.sus, api.name.sus.getDiningInfo);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}

export function getActivityInfo() {
  const url = api.getUrl(api.type.sus, api.name.sus.getActivityInfo);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}

export function exportUserHotelInfo(params: IObj<any>, token: string, filename: string) {
  const url = api.getUrl(api.type.sus, api.name.sus.exportUserHotelInfo, params);
  return fileDownloader(url, { filename, headers: [{ name: 'token', value: token }] });
}

export function exportBackInfo(params: IObj<any>, token: string, filename: string) {
  const url = api.getUrl(api.type.sus, api.name.sus.exportBackInfo, params);
  return fileDownloader(url, { filename, headers: [{ name: 'token', value: token }] });
}

export function exportActivityInfo(token: string, filename: string) {
  const url = api.getUrl(api.type.sus, api.name.sus.exportActivityInfo);
  return fileDownloader(url, { filename, headers: [{ name: 'token', value: token }] });
}

export function exporDiningInfo(token: string, filename: string) {
  const url = api.getUrl(api.type.sus, api.name.sus.exporDiningInfo);
  return fileDownloader(url, {
    filename,
    headers: [{ name: 'token', value: token }],
    method: 'POST',
    contentType: 'application/json',
  });
}

<template>
  <div class="com-g-row-a1 gap-y-[20px]">
    <div>
      <radio-tab :tab-list="tabList" :tab="curTab" @change="handleChange"></radio-tab>
      <filter-comp class="com-table-filter" @action="actionFn" :tab="curTab" :key="curTab" />
    </div>
    <table-comp
      class="com-table-container"
      ref="tableCompRef"
      @action="actionFn"
      v-if="['1', '4'].includes(curTab)"
      :tab="curTab"
      :key="curTab"
    />
    <table-comp2 v-else-if="curTab === '2'" class="com-table-container" />
    <table-comp3 v-else class="com-table-container" />
  </div>
</template>

<script lang="ts" setup>
import FilterComp from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import TableComp2 from './comp/table/Table2.vue';
import TableComp3 from './comp/table/Table3.vue';
import { ACTION } from './constant.ts';
import { ref } from 'vue';
import { IActionData } from './type.ts';
import { IObj } from '@/types';
import RadioTab from '@/components/tab/ComRadioTabA.vue';

const tableCompRef = ref();
const tabList = [
  { name: '1', label: '酒店信息' },
  { name: '2', label: '就餐信息' },
  { name: '3', label: '活动信息' },
  { name: '4', label: '回程信息' },
];
const curTab = ref('1');

function handleChange(name: string) {
  curTab.value = name;
}

function actionFn(val: IActionData) {
  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
  }
}

function handleSearch(data?: IObj<any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

defineOptions({ name: 'PcHotelInfo' });
</script>

<style module lang="scss"></style>

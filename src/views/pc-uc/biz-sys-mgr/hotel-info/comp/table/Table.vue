<template>
  <div class="relative">
    <n-data-table
      class="h-full"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :pagination="pagination"
      :loading="loading"
      :row-key="(row: IPageItem) => row.id"
      v-model:checked-row-keys="checkedKeys"
      :render-cell="useEmptyCell"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { cols, cols2 } from './columns.ts';
import { IObj } from '@/types';
import { IPageItem } from '../../type.ts';
import { getUserHotelPage } from '../../fetchData.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';

const emits = defineEmits(['action']);
const props = defineProps({ tab: { type: String, default: '1' } });
const [loading, search] = useAutoLoading(true);
const columns = computed(() => (props.tab === '1' ? cols : cols2));
const tableData = ref<IPageItem[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件
let checkedKeys = ref<string[]>([]);

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };

  search(getUserHotelPage(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

defineExpose({
  getTableDataWrap,
  getTableData,
});

watch(
  () => props.tab,
  () => {
    getTableData();
  },
  { immediate: true }
);

defineOptions({ name: 'PcHotelInfoTableComp' });
</script>

<style module lang="scss"></style>

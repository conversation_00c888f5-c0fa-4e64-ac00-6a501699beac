import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '姓名',
    key: 'userName',
    align: 'center',
  },
  {
    title: '手机号',
    key: 'phone',
    align: 'center',
  },
  {
    title: '单位信息',
    key: 'unitName',
    align: 'center',
  },
  {
    title: '入住酒店',
    key: 'meetingHotelName',
    align: 'center',
  },
  {
    title: '入住时间',
    key: 'checkInTime',
    align: 'center',
  },
  {
    title: '离店时间',
    key: 'checkOutTime',
    align: 'center',
  },
];

export const cols1: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '就餐信息',
    key: 'info',
    align: 'center',
  },
  {
    title: '报名人数',
    key: 'signNum',
    align: 'center',
  },
  {
    title: '报名人信息',
    key: 'signInfo',
    align: 'center',
  },
];

export const cols2: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '姓名',
    key: 'userName',
    align: 'center',
  },
  {
    title: '手机号',
    key: 'phone',
    align: 'center',
  },
  {
    title: '单位信息',
    key: 'unitName',
    align: 'center',
  },
  {
    title: '回程车次/航班',
    key: 'hcbc',
    align: 'center',
  },
  {
    title: '回程时间',
    key: 'hcsj',
    align: 'center',
  },
];

export const cols3: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '活动',
    key: 'info',
    align: 'center',
  },
  {
    title: '报名人数',
    key: 'signNum',
    align: 'center',
  },
  {
    title: '报名人',
    key: 'signInfo',
    align: 'center',
  },
];

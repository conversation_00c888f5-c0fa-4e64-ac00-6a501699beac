<template>
  <div class="relative">
    <n-data-table
      class="h-full"
      remote
      striped
      :columns="cols1"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :loading="loading"
      v-model:checked-row-keys="checkedKeys"
      :render-cell="useEmptyCell"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import { cols1 } from './columns.ts';
import { getDiningInfo } from '../../fetchData.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';

const [loading, search] = useAutoLoading(true);
const tableData = ref<Record<string, any>[]>([]);
let checkedKeys = ref<string[]>([]);

function getTableData() {
  search(getDiningInfo()).then((res) => {
    tableData.value = res.data || [];
  });
}

getTableData();

defineOptions({ name: 'PcHotelInfoTable2Comp' });
</script>

<style module lang="scss"></style>

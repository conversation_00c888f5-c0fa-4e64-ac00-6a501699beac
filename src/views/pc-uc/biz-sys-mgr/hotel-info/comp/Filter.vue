<template>
  <div :class="$style['wrap']">
    <n-form :show-feedback="false" label-placement="left" v-if="['1', '4'].includes(tab)">
      <n-grid class="flex-1" :cols="12" :x-gap="20" :y-gap="20">
        <n-form-item-gi label="姓名" :span="2">
          <n-input v-model:value="filterForm.userName" maxlength="100" clearable />
        </n-form-item-gi>
        <n-form-item-gi label="手机号" :span="2">
          <n-input v-model:value="filterForm.phone" maxlength="100" clearable />
        </n-form-item-gi>
        <n-form-item-gi label="入住时间" :span="3" v-if="tab === '1'">
          <n-date-picker
            class="w-full"
            type="daterange"
            start-placeholder="请选择日期"
            end-placeholder="请选择日期"
            date-format="yyyy-MM-dd HH:mm:ss"
            :on-update:formatted-value="checkInTimeUpdate"
            clearable
          >
            <template #separator>至</template>
          </n-date-picker>
        </n-form-item-gi>
        <n-form-item-gi label="离店时间" :span="3" v-if="tab === '1'">
          <n-date-picker
            class="w-full"
            type="daterange"
            start-placeholder="请选择日期"
            end-placeholder="请选择日期"
            date-format="yyyy-MM-dd HH:mm:ss"
            :on-update:formatted-value="checkOutTimeUpdate"
            clearable
          >
            <template #separator>至</template>
          </n-date-picker>
        </n-form-item-gi>

        <n-form-item-gi :span="tab === '1' ? 2 : 8">
          <div class="w-full flex justify-end gap-[10px]">
            <n-button type="primary" :loading="exportLoading" @click="handleExport">
              {{ ACTION_LABEL.EXPORT }}
            </n-button>
          </div>
        </n-form-item-gi>
      </n-grid>
    </n-form>
    <div class="w-full flex justify-end" v-else>
      <n-button type="primary" :loading="exportLoading" @click="handleExport">
        {{ ACTION_LABEL.EXPORT }}
      </n-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../constant';
import { ref, watch } from 'vue';
import { trimObjNull } from '@/utils/obj.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { exporDiningInfo, exportActivityInfo, exportBackInfo, exportUserHotelInfo } from '../fetchData.ts';
import { useAuthPcStore } from '@/store/auth-pc';

const emits = defineEmits(['action']);
const props = defineProps({ tab: { type: String, default: '1' } });
const filterForm = ref(initForm());
const [exportLoading, wrapExportFn] = useAutoLoading();

function initForm() {
  return {
    userName: '',
    phone: '',
    checkInStartTime: null as string | null,
    checkInEndTime: null as string | null,
    checkOutStartTime: null as string | null,
    checkOutEndTime: null as string | null,
  };
}

function getFilterForm() {
  return trimObjNull(Object.assign({}, filterForm.value));
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}

function checkInTimeUpdate(value: string[] | null) {
  if (value) {
    filterForm.value.checkInStartTime = value ? value[0] + ' 00:00:00' : null;
    filterForm.value.checkInEndTime = value ? value[1] + ' 23:59:59' : null;
  } else {
    filterForm.value.checkInStartTime = null;
    filterForm.value.checkInEndTime = null;
  }
}

function checkOutTimeUpdate(value: string[] | null) {
  if (value) {
    filterForm.value.checkOutStartTime = value ? value[0] + ' 00:00:00' : null;
    filterForm.value.checkOutEndTime = value ? value[1] + ' 23:59:59' : null;
  } else {
    filterForm.value.checkOutStartTime = null;
    filterForm.value.checkOutEndTime = null;
  }
}

function handleExport() {
  const pcStore = useAuthPcStore();
  const token = pcStore.userInfo.token as string;
  if (props.tab === '1') {
    wrapExportFn(exportUserHotelInfo(getFilterForm(), token, '酒店信息'));
  } else if (props.tab === '2') {
    wrapExportFn(exporDiningInfo(token, '就餐信息'));
  } else if (props.tab === '3') {
    wrapExportFn(exportActivityInfo(token, '活动信息'));
  } else {
    wrapExportFn(exportBackInfo(getFilterForm(), token, '回程信息'));
  }
}

watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'PcHotelInfoFilterComp' });
</script>

<style module lang="scss">
.wrap {
  width: 100%;
  padding-bottom: 20px;
}
</style>

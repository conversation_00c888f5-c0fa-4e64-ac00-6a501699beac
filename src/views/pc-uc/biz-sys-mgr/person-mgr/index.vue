<template>
  <div class="com-g-row-a1 gap-y-[20px]">
    <filter-comp class="com-table-filter" @action="actionFn" />
    <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" />
    <add-form v-model:show="showDialog" @close="showDialog = false" @success="addSuccess" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import FilterComp from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import AddForm from './comp/addForm.vue';
import { ACTION } from './constant.ts';
import { IActionData } from './type.ts';
import { IObj } from '@/types';

const showDialog = ref(false);
const tableCompRef = ref();
const filterData = ref<Record<string, any>>({});

function actionFn(val: IActionData) {
  if (val.action === ACTION.SEARCH) {
    filterData.value = val.data;
    handleSearch(val.data);
  } else if (val.action === ACTION.ADD) {
    showDialog.value = true;
  }
}

function handleSearch(data?: IObj<any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

function addSuccess() {
  showDialog.value = false;
  handleSearch(filterData.value);
}

defineOptions({ name: 'PcPersonMgr' });
</script>

<style module lang="scss"></style>

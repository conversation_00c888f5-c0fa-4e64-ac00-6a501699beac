<template>
  <div :class="$style['wrap']">
    <n-form :show-feedback="false" label-placement="left">
      <n-grid class="flex-1" :cols="12" :x-gap="20" :y-gap="20">
        <n-form-item-gi label="姓名" :span="2">
          <n-input v-model:value="filterForm.userName" maxlength="100" clearable />
        </n-form-item-gi>
        <n-form-item-gi label="手机号" :span="3">
          <n-input v-model:value="filterForm.phone" maxlength="100" clearable />
        </n-form-item-gi>

        <n-form-item-gi :span="7">
          <div class="w-full flex justify-end gap-[10px]">
            <n-button type="primary" @click="doHandle(ACTION.ADD)">
              {{ ACTION_LABEL.ADD }}
            </n-button>
            <n-button type="primary" :loading="exportLoading" @click="handleExport">
              {{ ACTION_LABEL.EXPORT }}
            </n-button>
          </div>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </div>
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../constant';
import { onMounted, ref, watch } from 'vue';
import { trimObjNull } from '@/utils/obj.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { fileExport } from '../fetchData.ts';
import { useAuthPcStore } from '@/store/auth-pc';

const emits = defineEmits(['action']);
const initForm = () => {
  return { userName: '', phone: '' };
};
const filterForm = ref(initForm());
const [exportLoading, wrapExportFn] = useAutoLoading();

function getFilterForm() {
  return trimObjNull(Object.assign({}, filterForm.value));
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}

function handleExport() {
  const pcStore = useAuthPcStore();
  wrapExportFn(fileExport(getFilterForm(), pcStore.userInfo.token as string, '内部人员'));
}

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'PcPersonMgrFilterComp' });
</script>

<style module lang="scss">
.wrap {
  width: 100%;
  padding-bottom: 20px;
}
</style>

<template>
  <ComDialog :width="600" :height="300" :maskClosable="false">
    <template #header>
      <div class="font-bold text-[18px]">新增账号</div>
    </template>

    <n-form
      class="mt-[20px] px-[20px]"
      ref="formInstRef"
      label-width="84"
      :model="formData"
      :rules="rules"
      label-placement="left"
    >
      <n-form-item label="姓名:" path="userName">
        <n-input v-model:value="formData.userName" clearable />
      </n-form-item>
      <n-form-item label="手机号:" path="phone">
        <n-input v-model:value="formData.phone" clearable />
      </n-form-item>
      <n-form-item label="选择角色:" path="userType">
        <n-radio-group v-model:value="formData.userType">
          <n-space>
            <n-radio value="2"> 志愿者 </n-radio>
            <n-radio value="1"> 管理员 </n-radio>
          </n-space>
        </n-radio-group>
      </n-form-item>
    </n-form>

    <template #action>
      <div class="flex justify-end">
        <n-button @click="cancelBtn">取消</n-button>
        <n-button type="primary" class="!ml-[20px]" @click="confirmBtn">确认</n-button>
      </div>
    </template>
  </ComDialog>
</template>

<script setup lang="ts">
import ComDialog from '@/components/dialog/ComDialog.vue';
import { ref } from 'vue';
import { FormInst, FormRules } from 'naive-ui';
import { isMobilePhone } from '@/utils/validate.ts';
import { addInsider } from '../fetchData.ts';

const emits = defineEmits(['close', 'success']);
const initForm = () => {
  return { userName: '', phone: '', userType: '2' };
};
const formData = ref(initForm());
const rules: FormRules = {
  userName: { required: true, message: '请输入姓名', trigger: 'blur' },
  phone: {
    required: true,
    validator(rule, val) {
      if (!val) return new Error('请输入手机号码');
      return isMobilePhone(val) ? true : new Error('请输入正确的手机号码');
    },
    trigger: 'blur',
  },
  userType: { required: true },
};
const formInstRef = ref<FormInst | null>(null);

function cancelBtn() {
  emits('close');
  formData.value = initForm();
}

function confirmBtn() {
  formInstRef.value?.validate((errors) => {
    if (!errors) {
      addInsider(formData.value).then(() => {
        emits('success');
        formData.value = initForm();
      });
    }
  });
}
</script>

<style scoped lang="scss"></style>

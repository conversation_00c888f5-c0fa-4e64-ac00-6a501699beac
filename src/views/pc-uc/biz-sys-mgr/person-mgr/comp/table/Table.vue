<template>
  <div class="relative">
    <n-data-table
      class="h-full"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :pagination="pagination"
      :loading="loading"
      :row-key="(row: any) => row.id"
      v-model:checked-row-keys="checkedKeys"
      :render-cell="useEmptyCell"
    />
  </div>
</template>

<script lang="ts" setup>
import { $dialog } from '@/common/shareContext';
import { cols } from './columns.ts';
import { DataTableColumns, NButton } from 'naive-ui';
import { IObj } from '@/types';
import { delInsider, getInsiderPage } from '../../fetchData.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { VNode, ref, h } from 'vue';

const emits = defineEmits(['action']);
const [loading, search] = useAutoLoading(true);
const columns = ref<DataTableColumns>([]);
const tableData = ref<Record<string, any>[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件
let checkedKeys = ref<string[]>([]);

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };

  search(getInsiderPage(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 240,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => deleteData(row),
        },
        { default: () => '删除' }
      ),
    ],
  ];

  return useActionDivider(acList);
}

// 删除
function deleteData(row: IObj<any>) {
  $dialog.error({
    title: '删除',
    content: '确认删除后，该数据无法恢复！',
    positiveText: '确定',
    onPositiveClick: async () => {
      delInsider({ id: row.id }).then(() => {
        getTableData();
      });
    },
  });
}

setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'PcPersonMgrTableComp' });
</script>

<style module lang="scss"></style>

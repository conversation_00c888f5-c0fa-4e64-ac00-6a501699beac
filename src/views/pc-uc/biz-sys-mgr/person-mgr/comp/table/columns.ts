import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '姓名',
    key: 'userName',
    align: 'center',
  },
  {
    title: '手机号',
    key: 'phone',
    align: 'center',
  },
  {
    title: '角色',
    key: 'userType',
    align: 'center',
    render(row) {
      if (row.userType === '0') return '参会人员';
      if (row.userType === '1') return '管理员';
      if (row.userType === '2') return '志愿者';
      return '--';
    },
  },
  {
    title: '创建时间',
    key: 'createTime',
    align: 'center',
  },
];

import { api } from '@/api';
import { IObj } from '@/types';
import { $http } from '@tanzerfe/http';
import { fileDownloader } from '@/views/pc/common/response';

export function getInsiderPage(query: IObj<any>) {
  const url = api.getUrl(api.type.sus, api.name.sus.getInsiderPage, query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}

export function addInsider(params: IObj<any>) {
  const url = api.getUrl(api.type.sus, api.name.sus.addInsider);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...params } });
}
//
export function delInsider(params: IObj<any>) {
  const url = api.getUrl(api.type.sus, api.name.sus.delInsider);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...params } });
}
//
export function fileExport(params: IObj<any>, token: string, filename?: string) {
  const url = api.getUrl(api.type.sus, api.name.sus.exportInsider, params);
  return fileDownloader(url, { filename, headers: [{ name: 'token', value: token }] });
}

<template>
  <div :class="$style.detail">
    <div :class="$style['main']" class="com-container-bg">
      <n-spin :show="loading">
        <n-card title="报名签到信息" class="mb-[20px]">
          <n-grid :cols="4" :x-gap="20" :y-gap="20" class="card-item">
            <n-gi>
              <div>姓名：{{ infoDetail.userName }}</div>
            </n-gi>
            <n-gi>
              <div>手机号：{{ infoDetail.phone }}</div>
            </n-gi>
            <n-gi>
              <div>是否学生：{{ getStudentLabel(infoDetail.studentStatus) }}</div>
            </n-gi>
            <n-gi>
              <div>性别：{{ getGenderLabel(infoDetail.gender) }}</div>
            </n-gi>
            <n-gi>
              <div>职务：{{ infoDetail.position }}</div>
            </n-gi>
            <n-gi>
              <div>职称：{{ infoDetail.positionRank }}</div>
            </n-gi>
            <n-gi>
              <div>工作单位：{{ infoDetail.workUnit }}</div>
            </n-gi>
            <n-gi>
              <div>电子邮箱：{{ infoDetail.mail }}</div>
            </n-gi>
            <n-gi>
              <div>参会会场：{{ infoDetail.conferenceName }}</div>
            </n-gi>
            <n-gi>
              <div>签到状态：{{ getSignStatusLabel(infoDetail.signStatus) }}</div>
            </n-gi>
            <n-gi>
              <div>签到时间：{{ infoDetail.signTime }}</div>
            </n-gi>
          </n-grid>
        </n-card>

        <n-card title="发票信息" class="mb-[20px]">
          <div :class="$style['btn']">
            <n-button type="primary" @click="operateBtn(infoDetail.invoiceStatus)">
              {{ infoDetail.invoiceStatus === '1' ? ACTION_LABEL.CONFIRM : ACTION_LABEL.FILL }}</n-button
            >
          </div>
          <n-grid :cols="3" :x-gap="20" :y-gap="20" class="card-item">
            <n-gi>
              <div>发票类型：{{ infoDetail.invoiceName }}</div>
            </n-gi>
            <n-gi>
              <div>发票抬头：{{ infoDetail.corporateName }}</div>
            </n-gi>
            <n-gi>
              <div>纳税人识别号：{{ infoDetail.acount }}</div>
            </n-gi>
            <n-gi>
              <div>注册地址及电话：{{ infoDetail.registerAddress + infoDetail.registerMobile }}</div>
            </n-gi>
            <n-gi>
              <div>开户银行及账号：{{ infoDetail.bank + infoDetail.openAccount }}</div>
            </n-gi>
            <n-gi>
              <div>取票方式：{{ getTicketDeliverLabel(infoDetail.ticketDelivery) }}</div>
            </n-gi>
            <n-gi>
              <div>取票人手机：{{ infoDetail.checkTakerMobile }}</div>
            </n-gi>
            <n-gi>
              <div>收票人邮箱：{{ infoDetail.checkTakerMail }}</div>
            </n-gi>
            <n-gi>
              <div>发票号：{{ infoDetail.invoiceNumber }}</div>
            </n-gi>
            <n-gi>
              <div>开票日期：{{ infoDetail.invoiceTime }}</div>
            </n-gi>
          </n-grid>
        </n-card>

        <n-card title="缴费信息">
          <n-grid :cols="4" :x-gap="20" :y-gap="20" class="card-item">
            <n-gi>
              <div>缴费金额：{{ payRecord.payMoney }}</div>
            </n-gi>
            <n-gi>
              <div>缴费方式：{{ getPayTypeLabel(payRecord.payType) }}</div>
            </n-gi>
            <n-gi>
              <div>缴费时间：{{ payRecord.createTime }}</div>
            </n-gi>
            <n-gi>
              <div>支付订单：{{ payRecord.outTradeNo }}</div>
            </n-gi>
          </n-grid>
        </n-card>
      </n-spin>
    </div>

    <fill-comp :info="infoDetail" v-model:show="showDialog" @cancel="showDialog = false" @confirm="confirmBtn" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import { fillInvoiceNumber, getMeetingUserDetail } from './fetchData.ts';
import { ACTION_LABEL } from './constant.ts';
import { $dialog } from '@/common/shareContext';
import FillComp from './comp/fill.vue';

const route = useRoute();
const id = route.query.id as string;
const loading = ref(true);
const showDialog = ref(false);
const infoDetail = ref<Record<string, any>>({});
const payRecord = ref<Record<string, any>>({});

function getStudentLabel(status: string) {
  if (status === '1') return '是';
  if (status === '0') return '否';
}

function getGenderLabel(status: string) {
  if (status === '1') return '女';
  if (status === '0') return '男';
}

function getSignStatusLabel(status: string) {
  if (status === '1') return '已签到';
  if (status === '0') return '未签到';
}

function getTicketDeliverLabel(type: string) {
  if (type === '0') return '电子票据';
}

function getPayTypeLabel(type: string) {
  if (type === '1') return '微信支付';
  if (type === '2') return '支付宝';
}

function operateBtn(type: string) {
  type === '1' ? confirm() : (showDialog.value = true);
}

function confirmBtn() {
  showDialog.value = false;
  getDetailData();
}

function confirm() {
  $dialog.warning({
    title: '作废确认',
    content: '是否作废，确认后会删除已填写的发票号信息？',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      const params = { ...infoDetail.value, invoiceNumber: '', invoiceStatus: '0' };
      fillInvoiceNumber(params).then(() => {
        getDetailData();
      });
    },
    onNegativeClick: () => {},
  });
}

function getDetailData() {
  getMeetingUserDetail(id)
    .then((res) => {
      infoDetail.value = res.data.meetingUser;
      payRecord.value = res.data.payRecord;
    })
    .finally(() => (loading.value = false));
}

getDetailData();

defineOptions({ name: 'PcSignInfoDetail' });
</script>

<style module lang="scss">
.detail {
  width: 100%;
  height: 100%;
  padding: 20px 24px;

  .main {
    width: 100%;
    height: 100%;
    padding: 10px;

    :global(.n-card) {
      background-color: var(--skin-bg3);
    }

    :global(.card-item) {
      padding: 10px;
      background: rgba(17, 143, 131, 0.1);
    }

    .btn {
      position: absolute;
      right: 24px;
      top: 20px;
    }
  }
}
</style>

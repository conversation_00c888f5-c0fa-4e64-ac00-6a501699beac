import { ACTION } from './constant';
import { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据类型
export interface IPageItem extends IDetailRes {
  id: string;
}
export type IPageListRes = IPageRes<IPageItem>;

// 详情
export interface IDetailRes {
  cccp: string;
  cccplx: string;
  cccps: string;
  cccpzs: string;
  ccjg: string;
  ccjgdm: string;
  cjsj: string;
  fileIds: string;
  gxsj: string;
  id: string;
  jhmc: string;
  sccj: string;
  ssrq: string;
  zddw: string;
  zt: string;
  ztdm: string;
  files: { filePath: string; fileName: string }[];
}

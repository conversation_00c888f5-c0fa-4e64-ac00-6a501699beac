<template>
  <div class="com-g-row-a1 gap-y-[20px]">
    <filter-comp class="com-table-filter" @action="actionFn" />
    <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" />

    <fill-comp :info="editData" v-model:show="showDialog" @cancel="showDialog = false" @confirm="confirmBtn" />

    <ComDialog v-model:show="qrcodeShow" :width="360" :height="360" :maskClosable="false">
      <template #header>
        <div class="font-bold text-[18px]">签到二维码</div>
      </template>
      <img class="w-[300px] h-[300px] mx-auto object-contain" :src="qrcodeAddress" alt="" />
    </ComDialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import FillComp from './comp/fill.vue';
import FilterComp from './comp/Filter.vue';
import TableComp from './comp/table/Table.vue';
import { ACTION } from './constant.ts';
import { IActionData } from './type.ts';
import { IObj } from '@/types';
import { useRouter } from 'vue-router';
import { $dialog } from '@/common/shareContext';
import { fillInvoiceNumber } from './fetchData.ts';
import ComDialog from '@/components/dialog/ComDialog.vue';
import noCode from '@/assets/no_qrcode.png';

const router = useRouter();
const showDialog = ref(false);
const qrcodeShow = ref(false);
const qrcodeAddress = ref('');
const tableCompRef = ref();
const filterData = ref<Record<string, any>>({});
const editData = ref<Record<string, any>>({});

function actionFn(val: IActionData) {
  if (val.action === ACTION.SEARCH) {
    filterData.value = val.data;
    handleSearch(val.data);
  } else if (val.action === ACTION.DETAIL) {
    router.push({ name: 'pcSignInfoDetail', query: { id: val.data.id } });
  } else if (val.action === ACTION.FILL) {
    showDialog.value = true;
    editData.value = val.data;
  } else if (val.action === ACTION.CONFIRM) {
    editData.value = val.data;
    confirm();
  } else if (val.action === ACTION.QRCODE) {
    qrcodeShow.value = true;
    const url = val.data.qrcodeAddress;
    qrcodeAddress.value = url ? window.$SYS_CFG.apiBaseFile + url : noCode;
  }
}

function handleSearch(data?: IObj<any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

function confirmBtn() {
  showDialog.value = false;
  handleSearch(filterData.value);
}

function confirm() {
  $dialog.warning({
    title: '作废确认',
    content: '是否作废，确认后会删除已填写的发票号信息？',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: () => {
      const params = { ...editData.value, invoiceNumber: '', invoiceStatus: '0' };
      fillInvoiceNumber(params).then(() => {
        handleSearch(filterData.value);
      });
    },
    onNegativeClick: () => {},
  });
}

defineOptions({ name: 'PcSignInfo' });
</script>

<style module lang="scss"></style>

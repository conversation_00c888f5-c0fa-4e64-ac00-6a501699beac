import { api } from '@/api';
import { IObj } from '@/types';
import { $http } from '@tanzerfe/http';
import { fileDownloader } from '@/views/pc/common/response';

export function getMeetingUserPage(query: IObj<any>) {
  const url = api.getUrl(api.type.sus, api.name.sus.getMeetingUserPage, query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}

export function getMeetingUserDetail(meetingUserId: string) {
  const url = api.getUrl(api.type.sus, api.name.sus.getMeetingUserDetail, { meetingUserId });
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}

export function fillInvoiceNumber(params: IObj<any>) {
  const url = api.getUrl(api.type.sus, api.name.sus.fillInvoiceNumber);
  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...params } });
}

export function postDelete(params: IObj<any>) {
  // const url = api.getUrl(api.type.sus, api.name.sus.signInfoDel);
  // return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...params } });
}

export function fileExport(params: IObj<any>, token: string, filename: string) {
  const url = api.getUrl(api.type.sus, api.name.sus.exportMeetingUser, params);
  return fileDownloader(url, { filename, headers: [{ name: 'token', value: token }] });
}

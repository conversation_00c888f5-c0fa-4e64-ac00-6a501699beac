<template>
  <div :class="$style['wrap']">
    <n-form :show-feedback="false" label-placement="left">
      <div class="flex justify-between items-end">
        <n-grid class="flex-1" :cols="4" :x-gap="10" :y-gap="20">
          <n-form-item-gi label="姓名">
            <n-input v-model:value="filterForm.userName" maxlength="100" clearable />
          </n-form-item-gi>
          <n-form-item-gi label="手机号">
            <n-input v-model:value="filterForm.phone" maxlength="100" clearable />
          </n-form-item-gi>

          <n-form-item-gi label="报名状态">
            <n-select v-model:value="filterForm.status" :options="statusOpt" clearable />
          </n-form-item-gi>

          <n-form-item-gi label="是否学生">
            <n-select v-model:value="filterForm.studentStatus" :options="studentStatusOpt" clearable />
          </n-form-item-gi>
          <n-form-item-gi label="报名时间">
            <n-date-picker
              class="w-full"
              type="daterange"
              start-placeholder="请选择日期"
              end-placeholder="请选择日期"
              date-format="yyyy-MM-dd HH:mm:ss"
              :on-update:formatted-value="signUpTimeUpdate"
              clearable
            >
              <template #separator>至</template>
            </n-date-picker>
          </n-form-item-gi>
          <n-form-item-gi label="签到时间">
            <n-date-picker
              class="w-full"
              type="daterange"
              start-placeholder="请选择日期"
              end-placeholder="请选择日期"
              date-format="yyyy-MM-dd HH:mm:ss"
              :on-update:formatted-value="signInTimeUpdate"
              clearable
            >
              <template #separator>至</template>
            </n-date-picker>
          </n-form-item-gi>
          <n-form-item-gi label="缴费时间">
            <n-date-picker
              class="w-full"
              type="daterange"
              start-placeholder="请选择日期"
              end-placeholder="请选择日期"
              date-format="yyyy-MM-dd HH:mm:ss"
              :on-update:formatted-value="signUpPayTimeUpdate"
              clearable
            >
              <template #separator>至</template>
            </n-date-picker>
          </n-form-item-gi>
        </n-grid>

        <div class="w-[12%] flex justify-end">
          <n-button type="primary" :loading="exportLoading" @click="handleExport">
            {{ ACTION_LABEL.EXPORT }}
          </n-button>
        </div>
      </div>
    </n-form>
  </div>
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../constant';
import { onMounted, ref, watch } from 'vue';
import { trimObjNull } from '@/utils/obj.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { fileExport } from '../fetchData.ts';
import { useAuthPcStore } from '@/store/auth-pc';

const emits = defineEmits(['action']);

const filterForm = ref(initForm());
const [exportLoading, wrapExportFn] = useAutoLoading();
const statusOpt = [
  {
    label: '未报名',
    value: '0',
  },
  {
    label: '已报名',
    value: '1',
  },
  {
    label: '已支付',
    value: '2',
  },
  {
    label: '已签到',
    value: '3',
  },
];
const studentStatusOpt = [
  {
    label: '是',
    value: '1',
  },
  {
    label: '否',
    value: '0',
  },
];

function initForm() {
  return {
    userName: '',
    phone: '',
    status: null,
    studentStatus: null,
    signInStartTime: null as string | null,
    signInEndTime: null as string | null,
    signUpStartTime: null as string | null,
    signUpEndTime: null as string | null,
    signUpPayStartTime: null as string | null,
    signUpPayEndTime: null as string | null,
  };
}

function signUpTimeUpdate(value: string[] | null) {
  if (value) {
    filterForm.value.signUpStartTime = value ? value[0] + ' 00:00:00' : null;
    filterForm.value.signUpEndTime = value ? value[1] + ' 23:59:59' : null;
  } else {
    filterForm.value.signUpStartTime = null;
    filterForm.value.signUpEndTime = null;
  }
}

function signInTimeUpdate(value: string[] | null) {
  if (value) {
    filterForm.value.signInStartTime = value ? value[0] + ' 00:00:00' : null;
    filterForm.value.signInEndTime = value ? value[1] + ' 23:59:59' : null;
  } else {
    filterForm.value.signInStartTime = null;
    filterForm.value.signInEndTime = null;
  }
}

function signUpPayTimeUpdate(value: string[] | null) {
  if (value) {
    filterForm.value.signUpPayStartTime = value ? value[0] + ' 00:00:00' : null;
    filterForm.value.signUpPayEndTime = value ? value[1] + ' 23:59:59' : null;
  } else {
    filterForm.value.signUpPayStartTime = null;
    filterForm.value.signUpPayEndTime = null;
  }
}

function getFilterForm() {
  return trimObjNull(Object.assign({}, filterForm.value));
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}

function handleExport() {
  const pcStore = useAuthPcStore();
  wrapExportFn(fileExport(getFilterForm(), pcStore.userInfo.token as string, '参会人员'));
}

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

watch(filterForm.value, () => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'PcSignInfoFilterComp' });
</script>

<style module lang="scss">
.wrap {
  width: 100%;
  padding-bottom: 20px;
}
</style>

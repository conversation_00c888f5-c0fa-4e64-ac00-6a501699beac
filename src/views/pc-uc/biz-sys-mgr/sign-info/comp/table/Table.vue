<template>
  <div class="relative">
    <n-data-table
      class="h-full"
      remote
      striped
      :columns="columns"
      :data="tableData"
      :bordered="false"
      :flex-height="true"
      :pagination="pagination"
      :loading="loading"
      :row-key="(row: IPageItem) => row.id"
      v-model:checked-row-keys="checkedKeys"
      :render-cell="useEmptyCell"
      :scroll-x="2000"
    />
  </div>
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../../constant.ts';
import { cols } from './columns.ts';
import { DataTableColumns, NButton } from 'naive-ui';
import { IObj } from '@/types';
import { IPageItem } from '../../type.ts';
import { getMeetingUserPage } from '../../fetchData.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { VNode, ref, toRaw, h } from 'vue';

const emits = defineEmits(['action']);

const [loading, search] = useAutoLoading(true);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IPageItem[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件
let checkedKeys = ref<string[]>([]);

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };

  search(getMeetingUserPage(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 240,
    align: 'center',
    fixed: 'right',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => emits('action', { action: ACTION.QRCODE, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.QRCODE }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => emits('action', { action: ACTION.DETAIL, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.DETAIL }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () =>
            emits('action', { action: row.invoiceStatus === '1' ? ACTION.CONFIRM : ACTION.FILL, data: toRaw(row) }),
        },
        { default: () => (row.invoiceStatus === '1' ? ACTION_LABEL.CONFIRM : ACTION_LABEL.FILL) }
      ),
    ],
  ];

  return useActionDivider(acList);
}

setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'PcSignInfoTableComp' });
</script>

<style module lang="scss"></style>

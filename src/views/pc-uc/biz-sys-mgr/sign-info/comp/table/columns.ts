import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
    fixed: 'left',
  },
  {
    title: '姓名',
    key: 'userName',
    align: 'center',
  },
  {
    title: '手机号',
    key: 'phone',
    align: 'center',
  },
  {
    title: '缴费金额',
    key: 'payMoney',
    align: 'center',
  },
  {
    title: '缴费时间',
    key: 'signUpPayTime',
    align: 'center',
    width: 180,
  },
  {
    title: '开票状态',
    key: 'invoiceStatus',
    align: 'center',
    render(row) {
      if (row.invoiceStatus === '1') return '已开票';
      if (row.invoiceStatus === '0' || !row.invoiceStatus) return '未开票';
      return '--';
    },
  },
  {
    title: '发票号',
    key: 'invoiceNumber',
    align: 'center',
  },
  {
    title: '开票日期',
    key: 'invoiceTime',
    align: 'center',
    width: 180,
  },
  {
    title: '单位信息',
    key: 'workUnit',
    align: 'center',
  },
  {
    title: '是否学生',
    key: 'studentStatus',
    align: 'center',
    render(row) {
      if (row.studentStatus === '1') return '是';
      if (row.studentStatus === '0') return '否';
      return '--';
    },
  },
  {
    title: '报名时间',
    key: 'createTime',
    align: 'center',
    width: 180,
  },
  {
    title: '报名状态',
    key: 'status',
    align: 'center',
    render(row) {
      if (row.status === '0') return '未报名';
      if (row.status === '1') return '已报名';
      if (row.status === '2') return '已支付';
      if (row.status === '3') return '已签到';
      return '--';
    },
  },
  {
    title: '签到状态',
    key: 'signStatus',
    align: 'center',
    render(row) {
      if (row.signStatus === '1') return '已签到';
      if (row.signStatus === '0') return '未签到';
      return '--';
    },
  },
  {
    title: '签到时间',
    key: 'signTime',
    align: 'center',
    width: 180,
  },
];

<template>
  <ComDialog title="发票号填写" :width="500" :height="150" :maskClosable="false">
    <template #header>
      <div class="font-bold text-[18px]">发票号填写</div>
    </template>
    <div class="mt-[20px] px-[20px]">
      <n-input v-model:value="invoiceNumber" placeholder="请输入发票号" />
    </div>

    <template #action>
      <div class="flex justify-end">
        <n-button @click="cancelBtn">取消</n-button>
        <n-button type="primary" class="!ml-[20px]" @click="confirmBtn">确认</n-button>
      </div>
    </template>
  </ComDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ComDialog from '@/components/dialog/ComDialog.vue';
import { fillInvoiceNumber } from '../fetchData.ts';
import { useToastCtx } from '@/common/shareContext';

const $toast = useToastCtx();
const props = defineProps({ info: { type: Object, default: () => {} } });
const emits = defineEmits(['cancel', 'confirm']);
const invoiceNumber = ref('');

function cancelBtn() {
  invoiceNumber.value = '';
  emits('cancel');
}

function confirmBtn() {
  if (!invoiceNumber.value) return $toast.warning('请输入发票号');
  const params = { ...props.info, invoiceNumber: invoiceNumber.value, invoiceStatus: '1' };
  fillInvoiceNumber(params).then(() => {
    invoiceNumber.value = '';
    emits('confirm');
  });
}

defineOptions({ name: 'fillComp' });
</script>

<style scoped lang="scss"></style>

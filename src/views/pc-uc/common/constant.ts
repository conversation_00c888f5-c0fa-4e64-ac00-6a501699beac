export const enum PROVIDE_KEY {
  currentAction = 'currentAction',
}

export const enum ACTION {
  SEARCH = 'SEARCH',
  ADD = 'ADD',
  DETAIL = 'DETAIL',
  EXPORT = 'EXPORT', // 导出
  IMPORT = 'IMPORT', // 导入
  DOWNLOAD = 'DOWNLOAD', // 模板下载
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  AUDIT = 'AUDIT', // 审核
  PMS = 'PMS',
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
  RESET_PASSWORD = 'RESET_PASSWORD',
}

export const ACTION_LABEL: { [key in ACTION]: string } = {
  [ACTION.SEARCH]: '查询',
  [ACTION.ADD]: '新增',
  [ACTION.DETAIL]: '详情',
  [ACTION.EXPORT]: '导出',
  [ACTION.IMPORT]: '导入',
  [ACTION.DOWNLOAD]: '模板下载',
  [ACTION.EDIT]: '编辑',
  [ACTION.DELETE]: '删除',
  [ACTION.AUDIT]: '审核',
  [ACTION.PMS]: '权限配置',
  [ACTION.ENABLE]: '启用',
  [ACTION.DISABLE]: '停用',
  [ACTION.RESET_PASSWORD]: '重置密码',
};

export const ACTION_TIP_CONTENT: Partial<{ [key in ACTION]: string }> = {
  [ACTION.DELETE]: '删除后数据无法恢复，请确认是否删除',
  [ACTION.AUDIT]: '审核通过后内容将发布于网站首页，请确认审核是否通过',
};

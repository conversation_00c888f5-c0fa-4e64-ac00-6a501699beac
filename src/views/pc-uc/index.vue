<template>
  <div class="grid place-items-center place-content-center">
    <span class="text-[18px] text-skin-t6">{{ tip }}</span>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { ref } from 'vue';

const router = useRouter();
const tip = ref('系统资源加载中...');

// tip.value = '无菜单访问权限，请联系管理员授权！';

router.push({ name: 'pcPortalMgrPortalBanner' });

defineOptions({ name: 'PcUcIndex' });
</script>

<style module lang="scss"></style>

<template>
  <n-form
    ref="formInstRef"
    :model="formData"
    :rules="rules"
    label-placement="left"
    label-width="110"
    require-mark-placement="left"
    class="pt-[30px] px-[20px]"
  >
    <n-form-item label="用户名" path="userName">
      <n-input v-model:value="formData.userName" clearable maxlength="20" />
    </n-form-item>

    <n-form-item label="手机号" path="userTelphone">
      <n-input
        v-model:value="formData.userTelphone"
        :allow-input="onlyAllowNumber"
        @blur="validatePhoneNum('userTelphone', 0)"
        :status="validatePhoneNum('userTelphone', 1)"
        clearable
        maxlength="11"
        show-count
      />
    </n-form-item>
    <n-form-item label="所属机构" path="orgCode">
      <n-cascader
        v-model:value="formData.orgCode"
        :show-path="false"
        :options="orgTree"
        value-field="id"
        label-field="text"
        children-field="children"
        clearable
        filterable
        :menuProps="{ class: 'n__checkbox-icon-circle' }"
      />
    </n-form-item>

    <n-form-item label="用户角色" path="roleId">
      <n-select
        v-model:value="formData.roleId"
        :options="roleSelectList"
        label-field="roleName"
        value-field="roleId"
        clearable
      />
    </n-form-item>

    <p v-if="isAdd" class="text-[#f00] text-center pt-[20px]">
      *新增用户默认密码为{{ DEFAULT_PWD.replace(/_/g, '') }}，建议首次登录后修改密码
    </p>
  </n-form>
</template>

<script setup lang="ts">
import { $toast } from '@/common/shareContext';
import { ACTION, PROVIDE_KEY } from '../../../../common/constant.ts';
import { computed, inject, ref, Ref, watch } from 'vue';
import { FormInst, FormRules } from 'naive-ui';
import { getQueryRoleSelect, postSave } from '../../fetchData.ts';
import { IActionData, IRoleSelectVo } from '../../type.ts';
import { isMobilePhone, onlyAllowNumber } from '@/utils/validate.ts';
import { comGetOrgTree } from '@/views/pc/common/response';

const DEFAULT_PWD = 'A_b_c_1_2_3_4_5_6';

const props = defineProps({
  show: Boolean,
});

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const isAdd = computed(() => currentAction.value.action === ACTION.ADD);
const actionData = computed(() => currentAction.value.data);

const initForm = () => {
  return {
    userId: isEdit.value ? actionData.value.id : undefined, // 主键
    roleId: isEdit.value ? actionData.value.roleId : undefined,
    orgCode: isEdit.value ? actionData.value.orgCode : undefined,
    userName: isEdit.value ? actionData.value.userName : '',
    userTelphone: isEdit.value ? actionData.value.userTelphone : '',
  };
};

const formInstRef = ref<FormInst | null>(null);
const formData = ref(initForm()); // 表单数据
const { orgTree } = comGetOrgTree();
const roleSelectList = ref<IRoleSelectVo[]>([]);
const rules: FormRules = {
  userName: { required: true, message: '请输入用户名', trigger: 'blur' },
  userTelphone: { required: true, message: '请输入手机号', trigger: 'blur' },
  orgCode: { required: true, message: '请输选择所属机构', trigger: ['blur', 'change'] },
  roleId: { required: true, message: '请输选择用户角色', trigger: ['blur', 'change'] },
};

function getRoleSelectList() {
  getQueryRoleSelect().then((res) => {
    roleSelectList.value = res.data;
  });
}

function handleShow() {
  getRoleSelectList();
}

function handleSubmit() {
  return new Promise((resolve, reject) => {
    formInstRef.value?.validate(async (errors) => {
      if (!errors) {
        const params = Object.assign({}, formData.value);

        postSave(params)
          .then(() => {
            resolve('submitted');
            reset();
          })
          .catch(reject);
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0];
            if (first.message) {
              $toast.error(first.message);
            }
          } catch (e) {}
        }
        reject(errors);
      }
    });
  });
}

function reset() {
  formData.value = initForm();
  roleSelectList.value = [];
}

/**
 * 验证手机号
 * @param k 字段
 * @param flag 0-清空动作 1-表单状态
 */
function validatePhoneNum(k: 'userTelphone', flag: 0 | 1) {
  const v = formData.value[k] || '';

  // flag 为 0 时，如果输入无效，清空表单值
  if (flag === 0 && v && !isMobilePhone(v)) {
    formData.value[k] = '';
  }

  // flag 为 1 时，返回错误状态
  if (flag === 1) {
    return v.length === 11 && !isMobilePhone(v) ? 'error' : '';
  }
}

// on show
watch(
  () => props.show,
  (val) => {
    if (val) {
      handleShow();
    } else {
      reset();
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'PcBaseInfoMgrUserAsideModify' });
</script>

<style module lang="scss"></style>

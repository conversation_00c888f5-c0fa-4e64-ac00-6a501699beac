import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IPageListRes, IRoleSelectVo } from './type.ts';
import { IObj } from '@/types';

export function pageList(params: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.baseInfoUserPageList);

  return $http.post<IPageListRes>(url, { data: { _cfg: { showTip: true }, ...params } });
}

export function postSave(params: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.baseInfoUserSave);

  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...params } });
}

export function postDelete(query: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.baseInfoUserDelete, query);

  return $http.get(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

export function postResetPwd(params: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.resetPassword, params);

  return $http.get(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

export function postUpdateUserStatus(params: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.baseInfoUserUpdateUserStatus, params);

  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

// 用户角色下拉
export function getQueryRoleSelect() {
  const url = api.getUrl(api.type.dcs, api.name.dcs.baseInfoRoleQueryRoleSelect);

  return $http.get<IRoleSelectVo[]>(url, { data: { _cfg: { showTip: true } } });
}

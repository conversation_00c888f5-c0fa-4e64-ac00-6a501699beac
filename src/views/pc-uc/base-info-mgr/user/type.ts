import { ACTION } from '../../common/constant';
import { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据类型
export interface IPageItem {
  /**
   * 账户状态,数据字典:zhzt
   */
  accountStatusName?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 用户id
   */
  id?: string;
  /**
   * 组织机构code
   */
  orgCode?: string;
  /**
   * 组织机构名称
   */
  orgName?: string;
  /**
   * 角色id
   */
  roleId?: string;
  /**
   * 角色名称
   */
  roleName?: string;
  /**
   * 用户名
   */
  userName?: string;
  [property: string]: any;
}
export type IPageListRes = IPageRes<IPageItem>;

// 详情
export type IDetailRes = IPageItem;

/**
 * 角色下拉查询实体
 *
 * RoleSelectVo
 */
export interface IRoleSelectVo {
  /**
   * 角色id
   */
  roleId?: string;
  /**
   * 角色名
   */
  roleName?: string;
  [property: string]: any;
}

import { ACTION } from '../../common/constant';
import { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据类型
export interface IPageItem {
  id: string;
  userName: string;
  roleId: string;
  roleName: string;
  createTime: string;
  orgName: string;
  orgCode: string;
  accountStatusName: string;
}
export type IPageListRes = IPageRes<IPageItem>;

// 详情
export type IDetailRes = IPageItem;

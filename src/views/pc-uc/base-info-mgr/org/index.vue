<template>
  <div class="com-g-row-a1">
    <ComBread :data="breadData" />

    <div class="com-g-col-a1 gap-x-[20px]">
      <OrgTree @action="actionFn" />
      <table-comp class="com-table-container" ref="tableCompRef" @action="actionFn" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import ComBread from '@/components/breadcrumb/ComBread.vue';
import TableComp from './comp/table/Table.vue';
import { ACTION, PROVIDE_KEY } from '../../common/constant.ts';
import { provide, Ref, ref } from 'vue';
import { IActionData } from './type.ts';
import { IObj } from '@/types';
import { useBread } from '../../common/useBread.ts';
import OrgTree from './comp/OrgTree.vue';

const { breadData } = useBread();
const currentAction = ref<IActionData>({ action: ACTION.ADD, data: {} });
const isShowAside = ref(false);
const tableCompRef = ref();

// provide
provide<Ref<IActionData>>(PROVIDE_KEY.currentAction, currentAction);

function actionFn(val: IActionData) {
  currentAction.value = val;

  if (val.action === ACTION.SEARCH) {
    handleSearch(val.data);
  } else if ([ACTION.DETAIL, ACTION.EDIT, ACTION.ADD].includes(val.action)) {
    isShowAside.value = true;
  }
}

function handleSearch(data?: IObj<any>) {
  if (data) {
    tableCompRef.value?.getTableDataWrap(data);
  } else {
    tableCompRef.value?.getTableData();
  }
}

defineOptions({ name: 'PcBaseInfoMgrOrgIndex' });
</script>

<style module lang="scss"></style>

import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '用户名',
    key: 'userName',
    align: 'center',
    ellipsis: { tooltip: true, lineClamp: 2 },
  },
  {
    title: '角色',
    key: 'roleName',
    align: 'center',
    ellipsis: { tooltip: true, lineClamp: 2 },
  },
  {
    title: '创建时间',
    key: 'createTime',
    align: 'center',
    width: 200,
  },
  {
    title: '所属机构',
    key: 'orgName',
    align: 'center',
    ellipsis: { tooltip: true, lineClamp: 2 },
  },
  {
    title: '账号状态',
    key: 'accountStatusName',
    align: 'center',
  },
];

<template>
  <div :class="$style['wrap']">
    <n-form :show-feedback="false" label-placement="left">
      <n-grid class="flex-1" :cols="18" :x-gap="30" :y-gap="20">
        <n-form-item-gi :span="4" label="角色名">
          <n-input v-model:value="filterForm.roleName" clearable />
        </n-form-item-gi>

        <n-form-item-gi :span="6" label="创建时间">
          <n-date-picker
            class="w-full"
            v-model:value="filterForm.rangeDateTime"
            type="datetimerange"
            :default-time="['00:00:00', '23:59:59']"
            clearable
          />
        </n-form-item-gi>

        <n-form-item-gi label="角色状态" :span="4">
          <n-select
            v-model:value="filterForm.roleStatus"
            :options="jsztOpt"
            label-field="dictLabel"
            value-field="dictValue"
            clearable
          />
        </n-form-item-gi>

        <n-form-item-gi :span="4">
          <div class="w-full flex justify-end gap-[10px]">
            <n-button type="primary" @click="doHandle(ACTION.SEARCH)">
              {{ ACTION_LABEL.SEARCH }}
            </n-button>
            <n-button type="primary" @click="doHandle(ACTION.ADD)">
              {{ ACTION_LABEL.ADD }}
            </n-button>
          </div>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </div>
</template>

<script lang="ts" setup>
import { ACTION, ACTION_LABEL } from '../../../common/constant';
import { onMounted, ref } from 'vue';
import { trimObjNull } from '@/utils/obj.ts';
import { formatTimestamp } from '@/utils/format.ts';
import { $dict } from '@/views/pc/common/dict/dict.ts';

const emits = defineEmits(['action']);

const filterForm = ref(initForm());
const { jsztOpt } = $dict.useJszt();

function initForm() {
  return {
    roleName: '',
    roleStatus: null,
    rangeDateTime: <null | [number, number]>null, // 时间
  };
}

function getFilterForm() {
  const { rangeDateTime } = filterForm.value;

  return trimObjNull(
    Object.assign({}, filterForm.value, {
      rangeDateTime: null,
      createStartTime: rangeDateTime ? formatTimestamp(rangeDateTime[0]) : null, // 开始时间
      createEndTime: rangeDateTime ? formatTimestamp(rangeDateTime[1]) : null, // 结束时间
    })
  );
}

function doHandle(action: ACTION) {
  emits('action', {
    action: action,
    data: getFilterForm(),
  });
}

onMounted(() => {
  doHandle(ACTION.SEARCH);
});

defineOptions({ name: 'PcUcBaseInfoMgrRoleFilter' });
</script>

<style module lang="scss">
.wrap {
  width: 100%;
  padding-bottom: 20px;
}
</style>

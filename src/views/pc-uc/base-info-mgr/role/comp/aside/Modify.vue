<template>
  <n-form
    ref="formInstRef"
    :model="formData"
    :rules="rules"
    label-placement="left"
    label-width="110"
    require-mark-placement="left"
    class="pt-[30px] px-[20px]"
  >
    <template v-if="!isPms">
      <n-form-item label="角色名称" path="roleName">
        <n-input v-model:value="formData.roleName" clearable maxlength="20" />
      </n-form-item>

      <n-form-item label="角色描述">
        <n-input v-model:value="formData.roleDesc" clearable maxlength="50" />
      </n-form-item>
    </template>

    <template v-if="!isEdit">
      <n-form-item label="角色权限">
        <n-checkbox-group v-model:value="formData.resourceIds">
          <n-flex vertical class="pt-[8px]" size="large">
            <n-checkbox v-for="item of menuList" :key="item.id" :value="item.id" :label="item.resName" />
          </n-flex>
        </n-checkbox-group>
      </n-form-item>
    </template>
  </n-form>
</template>

<script setup lang="ts">
import { $toast } from '@/common/shareContext';
import { ACTION, PROVIDE_KEY } from '../../../../common/constant.ts';
import { computed, inject, ref, Ref, watch } from 'vue';
import { FormInst, FormRules } from 'naive-ui';
import { postSave, postEditPermission, postQueryMenuList } from '../../fetchData.ts';
import { IActionData, IPmTResource } from '../../type.ts';
import { IObj } from '@/types';

const props = defineProps({
  show: Boolean,
});

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const isPms = computed(() => currentAction.value.action === ACTION.PMS);
const isAdd = computed(() => currentAction.value.action === ACTION.ADD);
const actionData = computed(() => currentAction.value.data);

const initForm = () => {
  return {
    id: isEdit.value || isPms.value ? actionData.value.id : undefined, // 主键
    roleId: isPms.value ? actionData.value.id : undefined,
    resourceIds: isPms.value ? actionData.value.resourceIds : undefined, // 角色权限id数组
    roleName: isEdit.value ? actionData.value.roleName : undefined,
    roleDesc: isEdit.value ? actionData.value.roleDesc : undefined,
  };
};

const formInstRef = ref<FormInst | null>(null);
const formData = ref(initForm()); // 表单数据
const rules: FormRules = {
  roleName: { required: true, message: '请输入角色名称', trigger: 'blur' },
  // roleDesc: { required: true, message: '请输入角色描述', trigger: 'blur' },
  // resourceIds: {
  //   required: true,
  //   message: '请选择角色权限',
  //   trigger: ['blur', 'change'],
  //   validator: (_, value: any) => {
  //     return !!value?.length;
  //   },
  // },
};
const menuList = ref<IPmTResource[]>([]);

function handleShow() {
  if (isPms.value || isAdd.value) {
    postQueryMenuList().then((res) => {
      menuList.value = res.data;
    });
  }
}

function handleSubmit() {
  return new Promise((resolve, reject) => {
    formInstRef.value?.validate(async (errors) => {
      if (!errors) {
        if (isPms.value) {
          const params = Object.assign({}, formData.value);

          postEditPermission(params)
            .then(() => {
              resolve('submitted');
              reset();
            })
            .catch(reject);
        } else {
          const params: IObj<any> = Object.assign({}, formData.value);

          postSave(params)
            .then(() => {
              resolve('submitted');
              reset();
            })
            .catch(reject);
        }
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0];
            if (first.message) {
              $toast.error(first.message);
            }
          } catch (e) {}
        }
        reject(errors);
      }
    });
  });
}

function reset() {
  formData.value = initForm();
  menuList.value = [];
}

// on show
watch(
  () => props.show,
  (val) => {
    if (val) {
      handleShow();
    } else {
      reset();
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'PcBaseInfoMgrRoleAsideModify' });
</script>

<style module lang="scss"></style>

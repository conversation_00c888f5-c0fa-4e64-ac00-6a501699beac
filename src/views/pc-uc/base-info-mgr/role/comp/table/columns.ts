import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '角色名',
    key: 'roleName',
    align: 'center',
    ellipsis: { tooltip: true, lineClamp: 2 },
  },
  {
    title: '角色描述',
    key: 'roleDesc',
    align: 'center',
    ellipsis: { tooltip: true, lineClamp: 2 },
  },
  {
    title: '创建时间',
    key: 'createTime',
    align: 'center',
    width: 200,
  },
  {
    title: '角色状态',
    key: 'roleStatusName',
    align: 'center',
  },
];

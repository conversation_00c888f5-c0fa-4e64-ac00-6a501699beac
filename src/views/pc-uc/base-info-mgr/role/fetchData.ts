import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IPageListRes, IPmTResource } from './type.ts';
import { IObj } from '@/types';

export function pageList(params: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.baseInfoRolePageList);

  return $http.post<IPageListRes>(url, { data: { _cfg: { showTip: true }, ...params } });
}

export function postSave(params: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.baseInfoRoleSave);

  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...params } });
}

export function postDelete(query: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.baseInfoRoleDelete, query);

  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

export function postUpdateRoleStatus(params: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.baseInfoRoleUpdateRoleStatus, params);

  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

// 查询菜单
export function postQueryMenuList() {
  const url = api.getUrl(api.type.dcs, api.name.dcs.baseInfoRoleQueryMenuList);

  return $http.post<IPmTResource[]>(url, { data: { _cfg: { showTip: true } } });
}

// 保存权限
export function postEditPermission(params: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.baseInfoRoleEditPermission);

  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...params } });
}

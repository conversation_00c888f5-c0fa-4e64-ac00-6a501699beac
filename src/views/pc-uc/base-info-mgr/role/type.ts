import { ACTION } from '../../common/constant';
import { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据类型
export interface IPageItem {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 角色id
   */
  id?: string;
  /**
   * 角色描述
   */
  roleDesc?: string;
  /**
   * 角色名称
   */
  roleName?: string;
  /**
   * 角色状态
   */
  roleStatus?: string;
  [property: string]: any;
}
export type IPageListRes = IPageRes<IPageItem>;

// 详情
export type IDetailRes = IPageItem;

/**
 * 菜单 PmTResource
 */
export interface IPmTResource {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人id
   */
  createUserId?: string;
  /**
   * 数据来源
   */
  dataRes?: string;
  /**
   * 删除标志（0正常 1删除）
   */
  delFlag?: string;
  /**
   * id
   */
  id: string;
  /**
   * 是否为管理员资源：0是1否
   */
  isAdmin?: string;
  /**
   * 是否是默认 0是 1否
   */
  isDefault?: string;
  /**
   * 资源状态（0:可见，1不可见）
   */
  isVisibled?: string;
  /**
   * 父资源ID
   */
  parentId?: string;
  /**
   * 资源别名
   */
  resAlias?: string;
  /**
   * 资源图标
   */
  resIcon?: string;
  /**
   * 资源标识
   */
  resIdent?: string;
  /**
   * 资源名称
   */
  resName: string;
  /**
   * 排序
   */
  resOrder?: number;
  /**
   * 请求方式 get,post,put,delete，暂留
   */
  resRequestType?: string;
  /**
   * 资源类型（0:目录，1：菜单，2：按钮）
   * 资源类型 0:目录，1：菜单，2：按钮
   */
  resType?: string;
  /**
   * 资源地址
   */
  resUrl?: string;
  /**
   * 应用系统code
   */
  sysCode?: string;
  /**
   * 修改时间
   */
  updateTime?: string;
  /**
   * 修改人
   */
  updateUserId?: string;
  /**
   * 是租户id,菜单定制的需要填,默认值：default
   */
  zhId?: string;
  [property: string]: any;
}

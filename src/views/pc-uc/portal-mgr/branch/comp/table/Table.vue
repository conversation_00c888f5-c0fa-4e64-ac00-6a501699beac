<template>
  <n-data-table
    class="h-full"
    remote
    striped
    :columns="columns"
    :data="tableData"
    :bordered="false"
    :flex-height="true"
    :pagination="pagination"
    :loading="loading"
    :render-cell="useEmptyCell"
  />
</template>

<script lang="ts" setup>
import { $dialog } from '@/common/shareContext';
import { ACTION, ACTION_LABEL, ACTION_TIP_CONTENT } from '../../../../common/constant.ts';
import { cols } from './columns.ts';
import { DataTableColumns, NButton } from 'naive-ui';
import { IObj } from '@/types';
import { IPageItem } from '../../type.ts';
import { pageList, postDelete, postAudit } from '../../fetchData.ts';
import { useActionDivider } from '@/common/hooks/useActionDivider.ts';
import { useAuthPcStore } from '@/store/auth-pc';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useEmptyCell } from '@/common/hooks/useEmptyCell.ts';
import { useNaivePagination } from '@/common/hooks/useNaivePagination.ts';
import { VNode, ref, toRaw, h, computed } from 'vue';

const emits = defineEmits(['action']);
const isSuperAdmin = computed(() => useAuthPcStore().isSuperAdmin);

const [loading, search] = useAutoLoading(true);
const columns = ref<DataTableColumns>([]);
const tableData = ref<IPageItem[]>([]);
const { pagination, updateTotal } = useNaivePagination(getTableData);

let filterData: IObj<any> = {}; // 搜索条件

function getTableData() {
  const params = {
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    ...filterData,
  };

  search(pageList(params)).then((res) => {
    tableData.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function getTableDataWrap(data: IObj<any>) {
  filterData = Object.assign({}, data) || {};
  pagination.page = 1;
  getTableData();
}

function setColumns() {
  columns.value.push(...cols);

  // 添加操作栏 action
  columns.value.push({
    title: '操作',
    key: 'actions',
    width: 240,
    align: 'center',
    render(row) {
      return getActionBtn(row);
    },
  });
}

function getActionBtn(row: any) {
  const acList: [VNode, boolean?][] = [
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => auditData(row),
        },
        { default: () => ACTION_LABEL.AUDIT }
      ),
      row.fbzt == '0' && isSuperAdmin.value,
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => emits('action', { action: ACTION.EDIT, data: toRaw(row) }),
        },
        { default: () => ACTION_LABEL.EDIT }
      ),
    ],
    [
      h(
        NButton,
        {
          text: true,
          class: 'com-action-button',
          onClick: () => deleteData(row),
        },
        { default: () => ACTION_LABEL.DELETE }
      ),
    ],
  ];

  return useActionDivider(acList);
}

// 审核
function auditData(row: IObj<any>) {
  $dialog.info({
    title: '审核',
    content: ACTION_TIP_CONTENT.AUDIT,
    positiveText: '通过',
    negativeText: '不通过',
    onPositiveClick: async () => {
      postAudit({ id: row.id, reviewStatus: '2' }).then(() => {
        getTableData();
      });
    },
    onNegativeClick: async () => {
      postAudit({ id: row.id, reviewStatus: '1' }).then(() => {
        getTableData();
      });
    },
  });
}

// 删除
function deleteData(row: IObj<any>) {
  $dialog.error({
    title: '删除',
    content: ACTION_TIP_CONTENT.DELETE,
    positiveText: '确定',
    onPositiveClick: async () => {
      postDelete({ id: row.id }).then(() => {
        getTableData();
      });
    },
  });
}

// on created
setColumns();

defineExpose({
  getTableDataWrap,
  getTableData,
});

defineOptions({ name: 'PcPortalBranchTable' });
</script>

<style module lang="scss"></style>

<template>
  <n-form
    ref="formInstRef"
    :model="formData"
    :rules="rules"
    label-placement="left"
    label-width="110"
    require-mark-placement="left"
    class="pt-[30px] px-[20px]"
  >
    <n-form-item label="机构名称" path="jgmc">
      <n-input v-model:value="formData.jgmc" clearable maxlength="20" show-count />
    </n-form-item>

    <n-form-item label="成立时间" path="clsj">
      <n-date-picker
        v-model:formatted-value="formData.clsj"
        type="date"
        value-format="yyyy-MM-dd HH:ss:mm"
        clearable
        class="!w-full"
      />
    </n-form-item>

    <n-form-item label="责任人" path="zrr">
      <n-input v-model:value="formData.zrr" clearable maxlength="10" show-count />
    </n-form-item>

    <n-form-item label="排序" path="px">
      <n-input-number v-model:value="formData.px" clearable :min="0" :max="9999" :show-button="false" class="!w-full" />
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import { $toast } from '@/common/shareContext';
import { ACTION, PROVIDE_KEY } from '../../../../common/constant.ts';
import { computed, inject, ref, Ref, watch } from 'vue';
import { FormInst } from 'naive-ui';
import { postSave } from '../../fetchData.ts';
import { IActionData } from '../../type.ts';

const props = defineProps({
  show: Boolean,
});

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const actionData = computed(() => currentAction.value.data);

const initForm = () => {
  return {
    id: isEdit.value ? actionData.value.id : undefined, // 主键
    jgmc: isEdit.value ? actionData.value.jgmc : '',
    clsj: isEdit.value ? actionData.value.clsj : null,
    zrr: isEdit.value ? actionData.value.zrr : '',
    px: isEdit.value ? actionData.value.px : null,
  };
};

const formInstRef = ref<FormInst | null>(null);
const formData = ref(initForm()); // 表单数据
const rules = {
  jgmc: { required: true, message: '请输入机构名称', trigger: 'blur' },
  clsj: { required: true, message: '请选择成立时间', trigger: 'blur' },
  zrr: { required: true, message: '请输入责任人', trigger: 'blur' },
  px: { required: true, message: '请输入排序', trigger: 'blur', type: 'number' },
};

function handleShow() {}

function handleSubmit() {
  return new Promise((resolve, reject) => {
    formInstRef.value?.validate(async (errors) => {
      if (!errors) {
        const params = Object.assign({}, formData.value);

        postSave(params)
          .then(() => {
            resolve('submitted');
            reset();
          })
          .catch(reject);
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0];
            if (first.message) {
              $toast.error(first.message);
            }
          } catch (e) {}
        }
        reject(errors);
      }
    });
  });
}

function reset() {
  formData.value = initForm();
}

// on show
watch(
  () => props.show,
  (val) => {
    if (val) {
      handleShow();
    } else {
      reset();
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'PcPortalBranchAsideModify' });
</script>

<style module lang="scss"></style>

<template>
  <n-form
    ref="formInstRef"
    :model="formData"
    :rules="rules"
    label-placement="left"
    label-width="110"
    require-mark-placement="left"
    class="pt-[30px] px-[20px]"
  >
    <n-form-item label="机构名称" path="jgmc">
      <n-input v-model:value="formData.jgmc" clearable maxlength="20" show-count />
    </n-form-item>

    <n-form-item label="机构简介" path="jgjj">
      <n-input v-model:value="formData.jgjj" clearable maxlength="100" show-count type="textarea" />
    </n-form-item>

    <n-form-item label="排序" path="px">
      <n-input-number v-model:value="formData.px" clearable :min="0" :max="9999" :show-button="false" class="!w-full" />
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import { $toast } from '@/common/shareContext';
import { ACTION, PROVIDE_KEY } from '../../../../common/constant.ts';
import { computed, inject, ref, Ref, watch } from 'vue';
import { FormInst, FormRules } from 'naive-ui';
import { postSave } from '../../fetchData.ts';
import { IActionData } from '../../type.ts';
import ImageUpload from '@/components/upload/image.vue';

const props = defineProps({
  show: Boolean,
});

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const actionData = computed(() => currentAction.value.data);

const initForm = () => {
  return {
    id: isEdit.value ? actionData.value.id : undefined, // 主键
    jgmc: isEdit.value ? actionData.value.jgmc : '',
    jgjj: isEdit.value ? actionData.value.jgjj : '',
    px: isEdit.value ? actionData.value.px : null,
  };
};

const formInstRef = ref<FormInst | null>(null);
const formData = ref(initForm()); // 表单数据
const rules = {
  bt: { required: true, message: '请输入标题', trigger: 'blur' },
  px: { required: true, message: '请输入排序', trigger: 'blur', type: 'number' },
  fileIds: {
    required: true,
    message: '请上传图片',
    trigger: 'blur',
    validator: (_: any, value: any) => {
      return !!fileIds.value;
    },
  },
};
const defaultFileList = computed(() => actionData.value.fileAttachmentList || []);
const fileList = ref<any[]>([]);
const fileIds = computed(() => fileList.value.map((item) => item.id).join(','));

function handleShow() {
  fileList.value = [...defaultFileList.value];
}

function handleFileListChange(list: any[]) {
  fileList.value = list.map((item) => item.res);
}

function handleSubmit() {
  return new Promise((resolve, reject) => {
    formInstRef.value?.validate(async (errors) => {
      if (!errors) {
        const params = Object.assign({}, formData.value, {
          xctFileId: fileIds.value, // 附件ids
        });

        postSave(params)
          .then(() => {
            resolve('submitted');
            reset();
          })
          .catch(reject);
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0];
            if (first.message) {
              $toast.error(first.message);
            }
          } catch (e) {}
        }
        reject(errors);
      }
    });
  });
}

function reset() {
  formData.value = initForm();
}

// on show
watch(
  () => props.show,
  (val) => {
    if (val) {
      handleShow();
    } else {
      reset();
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'PcPortalOfficeAsideModify' });
</script>

<style module lang="scss"></style>

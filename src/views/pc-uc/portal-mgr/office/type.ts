import { ACTION } from '../../common/constant';
import { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据类型
export interface IPageItem {
  /**
   * 发布时间
   */
  fbsj?: string;
  /**
   * 发布者
   */
  fbz?: string;
  /**
   * 发布状态值
   */
  fbzt?: string;
  /**
   * 发布状态
   */
  fbztName?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 机构简介
   */
  jgjj?: string;
  /**
   * 机构名称
   */
  jgmc?: string;
  /**
   * 排序
   */
  px?: number;
  /**
   * 审核人
   */
  shrName?: string;
  [property: string]: any;
}
export type IPageListRes = IPageRes<IPageItem>;

// 详情
export interface IDetailRes {
  id: string;
  bt: string;
  xct: string;
  px: number;
}

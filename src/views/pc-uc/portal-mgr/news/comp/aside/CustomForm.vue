<template>
  <n-form class="mt-[20px]" ref="formRef" label-placement="left" :model="formModel" :rules="formRules">
    <n-form-item label="类型名称：" path="dictLabel">
      <n-input v-model:value="formModel.dictLabel" placeholder="请输入" maxlength="20" show-count />
    </n-form-item>
  </n-form>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const formRef = ref();
const formModel = ref({ dictLabel: '' });
const formRules = {
  dictLabel: { required: true, message: '请输入类型名称' },
};

defineExpose({
  validate: () => formRef.value?.validate(),
  formModel,
});

defineOptions({ name: 'PcNewsAsideCustomForm' });
</script>

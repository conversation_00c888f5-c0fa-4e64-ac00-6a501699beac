<template>
  <n-form
    ref="formInstRef"
    :model="formData"
    :rules="rules"
    label-placement="left"
    label-width="110"
    require-mark-placement="left"
    class="pt-[30px] px-[20px]"
  >
    <n-form-item label="标题" path="bt">
      <n-input v-model:value="formData.bt" clearable maxlength="20" show-count />
    </n-form-item>

    <n-form-item label="类型" path="xgljlx">
      <n-select
        v-model:value="formData.xgljlx"
        placeholder="请选择"
        label-field="dictLabel"
        value-field="dictValue"
        clearable
        :options="xgljlxOpt"
      />
    </n-form-item>

    <n-form-item label="排序" path="px">
      <n-input-number v-model:value="formData.px" clearable :min="0" :max="9999" :show-button="false" class="!w-full" />
    </n-form-item>

    <n-form-item label="链接">
      <n-input
        v-model:value="formData.ljdz"
        clearable
        maxlength="1000"
        placeholder="请输入以 http(s):// 开头的链接地址"
      />
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import { $toast } from '@/common/shareContext';
import { ACTION, PROVIDE_KEY } from '../../../../common/constant.ts';
import { computed, inject, ref, Ref, watch } from 'vue';
import { FormInst } from 'naive-ui';
import { postSave } from '../../fetchData.ts';
import { IActionData } from '../../type.ts';
import { $dict } from '@/views/pc/common/dict/dict.ts';

const props = defineProps({
  show: Boolean,
});

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const actionData = computed(() => currentAction.value.data);
const { xgljlxOpt } = $dict.useXgljlx();

const initForm = () => {
  return {
    id: isEdit.value ? actionData.value.id : undefined, // 主键
    bt: isEdit.value ? actionData.value.bt : '',
    px: isEdit.value ? actionData.value.px : null,
    xgljlx: isEdit.value ? actionData.value.xgljlx : null,
    ljdz: isEdit.value ? actionData.value.ljdz : null,
  };
};

const formInstRef = ref<FormInst | null>(null);
const formData = ref(initForm()); // 表单数据
const rules = {
  bt: { required: true, message: '请输入标题', trigger: 'blur' },
  xgljlx: { required: true, message: '请选择类型', trigger: 'blur' },
  px: { required: true, message: '请输入排序', trigger: 'blur', type: 'number' },
};

function handleSubmit() {
  return new Promise((resolve, reject) => {
    formInstRef.value?.validate(async (errors) => {
      if (!errors) {
        const params = Object.assign({}, formData.value);

        postSave(params)
          .then(() => {
            resolve('submitted');
            reset();
          })
          .catch(reject);
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0];
            if (first.message) {
              $toast.error(first.message);
            }
          } catch (e) {}
        }
        reject(errors);
      }
    });
  });
}

function reset() {
  formData.value = initForm();
}

// on show
watch(
  () => props.show,
  (val) => {
    if (val) {
    } else {
      reset();
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'PcRelateLinkAsideModify' });
</script>

<style module lang="scss"></style>

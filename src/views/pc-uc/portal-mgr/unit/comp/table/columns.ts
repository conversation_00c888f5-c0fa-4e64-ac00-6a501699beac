import { DataTableColumn } from 'naive-ui';

export const cols: DataTableColumn[] = [
  {
    title: '序号',
    key: 'index',
    align: 'center',
    width: 65,
    render: (_: any, index: number) => {
      return index + 1;
    },
  },
  {
    title: '理事单位名称',
    key: 'dwmc',
    align: 'center',
    ellipsis: { tooltip: true, lineClamp: 2 },
  },
  {
    title: '类型',
    key: 'dwlxName',
    align: 'center',
    ellipsis: { tooltip: true, lineClamp: 2 },
  },
  {
    title: '发布者',
    key: 'fbz',
    align: 'center',
    ellipsis: { tooltip: true, lineClamp: 2 },
  },
  {
    title: '发布时间',
    key: 'fbsj',
    align: 'center',
    width: 200,
  },
  {
    title: '发布状态',
    key: 'fbztName',
    align: 'center',
  },
  {
    title: '审核人',
    key: 'shrName',
    align: 'center',
  },
  {
    title: '排序',
    key: 'px',
    align: 'center',
  },
];

import { $http } from '@tanzerfe/http';
import { api } from '@/api';
import { IPageListRes } from './type.ts';
import { IObj } from '@/types';

export function pageList(params: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.portalIntroductionPageList);

  return $http.post<IPageListRes>(url, { data: { _cfg: { showTip: true }, ...params } });
}

export function postSave(params: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.portalIntroductionSave);

  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...params } });
}

export function postDelete(query: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.portalIntroductionDelete, query);

  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true } } });
}

export function postAudit(params: IObj<any>) {
  const url = api.getUrl(api.type.dcs, api.name.dcs.portalIntroductionAudit);

  return $http.post(url, { data: { _cfg: { showTip: true, showOkTip: true }, ...params } });
}

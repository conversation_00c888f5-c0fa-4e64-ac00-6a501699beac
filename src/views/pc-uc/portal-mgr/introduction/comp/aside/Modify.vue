<template>
  <n-form
    ref="formInstRef"
    :model="formData"
    :rules="rules"
    label-placement="left"
    label-width="110"
    require-mark-placement="left"
    class="pt-[30px] px-[20px]"
  >
    <n-form-item label="标题" path="bt">
      <n-input v-model:value="formData.bt" clearable maxlength="20" show-count />
    </n-form-item>

    <n-form-item label="宣传图" path="fileIds">
      <ImageUpload
        :data="defaultFileList"
        :max="1"
        :size="5"
        tips="仅支持上传一张图片，图片大小限制5MB。"
        @update="handleFileListChange"
      />
    </n-form-item>

    <n-form-item label="正文" path="zw">
      <ComQuillEditor v-model="formData.zw" />
    </n-form-item>

    <n-form-item label="发展历程" path="introduceTitleList">
      <n-dynamic-input
        v-model:value="formData.introduceTitleList"
        :min="0"
        :max="99"
        :on-create="handleCreate"
        item-style="margin-bottom: 15px"
      >
        <template #default="{ index }">
          <div class="grid grid-cols-[1fr_auto_1fr] gap-[10px] items-center justify-center w-full">
            <n-form-item :show-label="false" :show-feedback="false">
              <n-input
                v-model:value="formData.introduceTitleList[index].bt"
                placeholder="请输入标题内容"
                maxlength="50"
                clearable
                @keydown.enter.prevent
                @blur="handleInputBlur"
              />
            </n-form-item>

            <span class="px-[10px] text-gray-400">——</span>

            <n-form-item :show-label="false" :show-feedback="false">
              <n-input
                v-model:value="formData.introduceTitleList[index].fbt"
                placeholder="请输入副标题内容"
                maxlength="50"
                clearable
                @keydown.enter.prevent
                @blur="handleInputBlur"
              />
            </n-form-item>
          </div>
        </template>
      </n-dynamic-input>
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import ComQuillEditor from '@/components/editor/QuillEditor.vue';
import ImageUpload from '@/components/upload/image.vue';
import { $toast } from '@/common/shareContext';
import { ACTION, PROVIDE_KEY } from '../../../../common/constant.ts';
import { computed, inject, nextTick, ref, Ref, watch } from 'vue';
import { FormInst } from 'naive-ui';
import { IActionData } from '../../type.ts';
import { nanoid } from 'nanoid';
import { postSave } from '../../fetchData.ts';

interface IIntroduceTitle {
  id: string;
  bt: string;
  fbt: string;
}

const props = defineProps({
  show: Boolean,
});

const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isEdit = computed(() => currentAction.value.action === ACTION.EDIT);
const actionData = computed(() => currentAction.value.data);

const initForm = () => {
  return {
    id: isEdit.value ? actionData.value.id : undefined, // 主键
    bt: isEdit.value ? actionData.value.bt : '',
    zw: isEdit.value ? actionData.value.zw : '', // 正文
    introduceTitleList: (isEdit.value ? actionData.value.portalIntroduceTitleList || [] : []) as IIntroduceTitle[],
  };
};

const formInstRef = ref<FormInst | null>(null);
const formData = ref(initForm()); // 表单数据
const rules = {
  bt: { required: true, message: '请输入标题', trigger: 'blur' },
  fileIds: {
    required: true,
    message: '请上传图片',
    trigger: 'blur',
    validator: (_: any, value: any) => {
      return !!fileIds.value;
    },
  },
  zw: { required: true, message: '请输入正文内容', trigger: 'blur' },
  introduceTitleList: {
    required: true,
    trigger: 'blur',
    key: 'introduceTitleList',
    validator: (_: any, value: any) => {
      const list = value || [];
      if (!list.length) {
        return Promise.reject('请添加发展历程');
      }

      // 检查是否有不完整的项目（bt或fbt为空）
      const hasIncompleteItems = list.some((item: any) => !item.bt || !item.fbt);
      if (hasIncompleteItems) {
        return Promise.reject('发展历程填写不完整');
      }

      return Promise.resolve();
    },
  },
};
const defaultFileList = computed(() => actionData.value.xctFileAttachmentList || []);
const fileList = ref<any[]>([]);
const fileIds = computed(() => fileList.value.map((item) => item.id).join(','));

function handleCreate(): IIntroduceTitle {
  return {
    id: nanoid(8),
    bt: '',
    fbt: '',
  };
}

function handleShow() {
  fileList.value = [...defaultFileList.value];
}

function handleFileListChange(list: any[]) {
  fileList.value = list.map((item) => item.res);
}

function handleInputBlur() {
  // 手动触发 introduceTitleList 字段的验证
  nextTick(() => {
    formInstRef.value?.validate(
      () => {},
      (rule) => {
        return rule.key === 'introduceTitleList';
      }
    );
  });
}

function handleSubmit() {
  return new Promise((resolve, reject) => {
    formInstRef.value?.validate(async (errors) => {
      if (!errors) {
        const params = Object.assign({}, formData.value, {
          xctFileId: fileIds.value, // 附件ids
        });

        postSave(params)
          .then(() => {
            resolve('submitted');
            reset();
          })
          .catch(reject);
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0];
            if (first.message) {
              $toast.error(first.message);
            }
          } catch (e) {}
        }
        reject(errors);
      }
    });
  });
}

function reset() {
  formData.value = initForm();
}

// on show
watch(
  () => props.show,
  (val) => {
    if (val) {
      handleShow();
    } else {
      reset();
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'PcPortalIntroductionAsideModify' });
</script>

<style module lang="scss"></style>

import { ACTION } from '../../common/constant';
import { IObj, IPageRes } from '@/types';

export interface IActionData {
  action: ACTION;
  data: IObj<any>;
}

// 分页列表数据类型
export interface IPageItem {
  id: string;
  bt: string;
  fbz: string;
  fbsj: string;
  fbzt: string;
  shr: string;
  px: number;
  [property: string]: any;
}
export type IPageListRes = IPageRes<IPageItem>;

// 详情
export interface IDetailRes {
  id: string;
  bt: string;
  xct: string;
  px: number;
}

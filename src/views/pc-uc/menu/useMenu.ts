import { IObj } from '@/types';
import type { IMenu } from '@/views/pc-uc/menu/type';
import { MenuOption, NIcon } from 'naive-ui';
import { h, ref, watch } from 'vue';
import { type LocationQueryRaw, useRouter } from 'vue-router';
import { RouteRecordRaw } from 'vue-router';
import * as IconMap from './assets/icon';
import { menuDataList } from './menu';
import { usePermsPcStore } from '@/store/perms/usePermsPcStore.ts';

export function useMenu() {
  const asyncRoutes = usePermsPcStore().asyncRoutes;
  const router = useRouter();
  const rName = router.currentRoute.value.name as string;
  const rQuery = router.currentRoute.value.query;
  const rMeta = router.currentRoute.value.meta;
  const activeKey = ref(addTabParamToNameIfPresent(rName, rQuery, rMeta));

  // 根据asyncRoutes过滤menuDataList，只保留有权限的菜单，如果子菜单都没有权限，父菜单也不显示
  const filteredMenuData = filterMenuByPermissions(menuDataList, asyncRoutes);
  const menuList = transMenuList(filteredMenuData);

  function handleUpdateValue(key: string, item: MenuOption) {
    activeKey.value = key;

    if (typeof item.routeName === 'string' && item.routeName) {
      const { routeName, routeQuery } = item;
      const name = processName(routeName);
      const query = routeQuery as LocationQueryRaw;

      router.push({ name, query });
    } else if (typeof item.url === 'string' && item.url) {
      window.open(item.url);
    }
  }

  watch(
    () => router.currentRoute.value,
    (val) => {
      if (val.name) {
        activeKey.value = addTabParamToNameIfPresent(<string>val.name, val.query, val.meta);
      }
    }
  );

  return { menuList, activeKey, handleUpdateValue };
}

/**
 * 根据权限路由过滤菜单数据 非分组label名支持后台定义
 * @param menuList 原始菜单数据
 * @param asyncRoutes 有权限的路由列表
 * @returns 过滤后的菜单数据
 */
function filterMenuByPermissions(menuList: IMenu[], asyncRoutes: RouteRecordRaw[]): IMenu[] {
  // 构建 routeName -> route 的映射
  const routeMap = new Map<string, RouteRecordRaw>();
  asyncRoutes.forEach((route) => {
    routeMap.set(route.name as string, route);
  });

  function filterMenuItem(item: IMenu): IMenu | null {
    const matchedRoute = item.routeName ? routeMap.get(item.routeName) : undefined;

    if (item.children && item.children.length > 0) {
      const filteredChildren = item.children.map(filterMenuItem).filter(Boolean) as IMenu[];

      if (filteredChildren.length > 0) {
        const firstChild = filteredChildren[0];
        const matchFirstChildRoute = routeMap.get(firstChild.routeName as string);

        if (matchFirstChildRoute?.meta?.groupName) {
          item.label = matchFirstChildRoute?.meta?.groupName || item.label;
        }

        return {
          ...item,
          label: matchedRoute?.meta?.label || item.label,
          children: filteredChildren,
        };
      } else {
        return matchedRoute
          ? {
              ...item,
              label: matchedRoute.meta?.label || item.label,
              children: [],
            }
          : null;
      }
    } else {
      return matchedRoute
        ? {
            ...item,
            label: matchedRoute.meta?.label || item.label,
          }
        : null;
    }
  }

  return menuList.map(filterMenuItem).filter(Boolean) as IMenu[];
}

// 转换menu列表数据 - 处理icon
function transMenuList(menus: IMenu[]) {
  return menus.map((menu) => {
    const menuOption = { ...menu };

    if (typeof menu.icon === 'string' && menu.icon) {
      menuOption.icon = renderMenuIcon(menu.icon);
    }

    if (menuOption.children) {
      const children = <IMenu[]>menuOption.children;
      menuOption.children = transMenuList(children);
    }

    return menuOption;
  });
}

function renderMenuIcon(k: string) {
  const iconKey = <keyof typeof IconMap>`icon_${k}`;
  const icon = IconMap[iconKey];

  return () =>
    h(NIcon, null, {
      default: () =>
        h(icon, {
          fontSize: '20px',
        }),
    });
}

function processName(name: string) {
  return name.replace(/\?.*$/, '');
}

/**
 * 还原tab参数
 * @param name
 * @param query
 * @param meta
 */
function addTabParamToNameIfPresent(name: string, query: LocationQueryRaw, meta: IObj<any>) {
  const includeTab = meta?.includeTab;
  return includeTab ? includeTab : name;
}

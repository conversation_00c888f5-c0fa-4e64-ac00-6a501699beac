import { iconBase } from '@/utils/svg.ts';

export const icon_expand = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="a"><rect width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#a)"><path d="M1.115 10.388l4.228 3.027c.**************.285.02a.277.277 0 0 0 .147-.249v-6.05a.277.277 0 0 0-.147-.248.271.271 0 0 0-.285.02L1.115 9.935a.28.28 0 0 0 0 .452zM8.125 6.8h10.083q.792 0 .792.8t-.792.8H8.125q-.792 0-.792-.8t.792-.8zm0 4.8h10.083q.792 0 .792.8t-.792.8H8.125q-.792 0-.792-.8t.792-.8zm-6.333 4.8h16.416q.792 0 .792.8t-.792.8H1.792Q1 18 1 17.2t.792-.8zm0-14.4h16.416Q19 2 19 2.8t-.792.8H1.792Q1 3.6 1 2.8t.792-.8z" fill="#606266"/></g></svg>`
  );

export const icon_sy = (props: any) =>
  iconBase(
    props,
    `<svg  fill="none"  width="20" height="20" viewBox="0 0 20 20"><g><g><path d="M11.5,17L11.5,13.6766C11.5,13.1521,11.05229,12.72701,10.5,12.72701L9.5,12.72701C8.94771,12.72701,8.5,13.1521,8.5,13.6766L8.5,17L4,17C3.447715,17,3,16.5749,3,16.0504L3,8.2812C3.0000698853,7.98449,3.1462,7.70487,3.395,7.52536L9.395,3.193495C9.75258,2.9355018,10.24742,2.9355018,10.605,3.193495L16.605,7.52536C16.8539,7.705,17,7.98462,17,8.2812L17,16.0504C17,16.5749,16.552300000000002,17,16,17L11.5,17Z" fill="#545A67" fill-opacity="1"/></g></g></svg>`
  );

export const icon_pzgl = (props: any) =>
  iconBase(
    props,
    `<svg  fill="none"  width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_1_8384"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_1_8384)"><g><path d="M17.26914,13.38107L11.730858,13.38107C11.324826,13.38107,11,13.07417,11,12.690536999999999C11,12.306905,11.324826,12,11.730858,12L17.26914,12C17.67517,12,18,12.306905,18,12.690536999999999C18,13.07417,17.67517,13.38107,17.26914,13.38107ZM17.26914,15.69821L11.730858,15.69821C11.324826,15.69821,11,15.391300000000001,11,15.007670000000001C11,14.62404,11.324826,14.31714,11.730858,14.31714L17.26914,14.31714C17.67517,14.31714,18,14.62404,18,15.007670000000001C18,15.391300000000001,17.67517,15.69821,17.26914,15.69821ZM17.26914,18L11.730858,18C11.324826,18,11,17.693089999999998,11,17.30946C11,16.92583,11.324826,16.61893,11.730858,16.61893L17.26914,16.61893C17.67517,16.61893,18,16.92583,18,17.30946C18,17.693089999999998,17.67517,18,17.26914,18Z" fill="#545A67" fill-opacity="1"/></g><g><path d="M17.8602,7.19424L16.4621,4.82156C16.1857,4.37268,15.6005,4.19633,15.1291,4.4368099999999995L13.9911,4.88569C13.5684,4.56506,13.0969,4.30855,12.593,4.11617L12.4304,2.945865C12.3979,2.4007899999999998,11.95895,2,11.42248,2L8.593779999999999,2C8.0573,2,7.61836,2.4007899999999998,7.58585,2.929834L7.42328,4.10014C6.93557,4.30855,6.46412,4.56506,6.02519,4.86966L4.88721,4.42077C4.415760000000001,4.1803,3.8305100000000003,4.35665,3.5541400000000003,4.80553L2.1397909999999998,7.19424C1.87968,7.64313,2.0259924,8.2363,2.481185,8.50884L3.44034,9.230260000000001C3.37531,9.743269999999999,3.37531,10.27231,3.44034,10.78533L2.464929,11.50675C2.00973549,11.77929,1.863423,12.3725,2.1397909999999998,12.8213L3.5541400000000003,15.194C3.8305100000000003,15.6429,4.415760000000001,15.8193,4.90346,15.5788L6.02519,15.1299C6.44787,15.4505,6.91932,15.707,7.40702,15.8994L7.56959,17.069699999999997C7.60211,17.5988,8.041039999999999,18.0156,8.57752,17.9996L9.69925,17.9996C9.86182,17.9996,9.97561,17.871299999999998,9.991869999999999,17.727L9.991869999999999,17.117800000000003L9.991869999999999,13.2542C8.18736,13.2702,6.70798,11.81135,6.70798,10.01581C6.70798,8.22027,8.18736,6.76139,10.00813,6.76139C11.8289,6.76139,13.3083,8.22027,13.3083,10.01581C13.3083,10.25628,13.2758,10.49676,13.227,10.73723L16.3808,10.73723C16.4946,10.73723,16.5922,10.64104,16.6084,10.52882C16.640900000000002,10.09597,16.640900000000002,9.67915,16.5759,9.24629L17.5351,8.52487C17.990299999999998,8.2363,18.1366,7.64313,17.8602,7.19424Z" fill="#545A67" fill-opacity="1"/></g></g></svg>`
  );

export const icon_sign = (props: any) =>
  iconBase(
    props,
    `<svg viewBox="0 0 48 48"><path fill="currentColor" d="M8 8.25A4.25 4.25 0 0 1 12.25 4h13.336a2.25 2.25 0 0 1 1.59.659l10.04 10.04a6 6 0 0 0-1.457 1.088l-.491.499L26.5 7.518v7.732c0 .69.56 1.25 1.25 1.25h7.307L32.595 19H27.75A3.75 3.75 0 0 1 24 15.25V6.5H12.25a1.75 1.75 0 0 0-1.75 1.75v31.5c0 .967.784 1.75 1.75 1.75h23.5a1.75 1.75 0 0 0 1.75-1.75v-8.891l2.5-2.462V39.75A4.25 4.25 0 0 1 35.75 44h-23.5A4.25 4.25 0 0 1 8 39.75zm29.182 8.94a3.981 3.981 0 1 1 5.63 5.63L29.09 36.33a3 3 0 0 1-1.351.766l-3.235.839a2 2 0 0 1-.593.064h-9.672a1.25 1.25 0 1 1 0-2.5h7.828l.838-3.236c.133-.51.397-.976.767-1.352z"></path></svg>`
  );

export const icon_hotel = (props: any) =>
  iconBase(
    props,
    `<svg viewBox="0 0 38 38"><path d="M9.5 15A1.5 1.5 0 1 1 8 16.5 1.5 1.5 0 0 1 9.5 15m0-2a3.5 3.5 0 1 0 3.5 3.5A3.5 3.5 0 0 0 9.5 13Z"></path><path d="M25 14h-8a2 2 0 0 0-2 2v6H4V10.6l12-6.46 12.53 6.74.94-1.76-13-7a1 1 0 0 0-.94 0l-13 7A1 1 0 0 0 2 10v20h2v-6h24v6h2V19a5 5 0 0 0-5-5Zm-8 8v-6h8a3 3 0 0 1 3 3v3Z"></path><path d="M0 0h32v32H0z" fill="none" style=""></path></svg>`
  );

export const icon_person = (props: any) =>
  iconBase(
    props,
    `<svg viewBox="0 0 48 48"><path fill="currentColor" d="M22 4c-5.523 0-10 4.477-10 10s4.477 10 10 10 10-4.477 10-10S27.523 4 22 4Zm-7.5 10a7.5 7.5 0 1 1 15 0 7.5 7.5 0 0 1-15 0Zm-4.25 14A4.25 4.25 0 0 0 6 32.249V33c0 3.755 1.942 6.567 4.92 8.38C13.85 43.163 17.785 44 21.998 44q.002-.493.132-1.003l.39-1.502q-.26.005-.52.005c-3.932 0-7.37-.788-9.78-2.255C9.86 37.808 8.5 35.745 8.5 33v-.751a1.75 1.75 0 0 1 1.75-1.749h20.897l2.462-2.5zm34.584-2.832a3.98 3.98 0 0 0-5.652.022L25.671 38.913c-.37.375-.634.841-.767 1.351l-.838 3.234c-.383 1.477.961 2.82 2.437 2.438l3.235-.839a3 3 0 0 0 1.351-.766L44.812 30.82a3.98 3.98 0 0 0 .022-5.651Z"></path></svg>`
  );

export const icon_mhxctgl = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><defs><clipPath id="master_svg0_36_3555"><rect x="0" y="0" width="16" height="16" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_36_3555)"><g><path d="M13.440025390625,8.80802734375C13.440025390625,8.72802734375,13.400025390625,8.65602734375,13.328025390625,8.61602734375L10.880025390625,7.12002734375C10.784025390625,7.06402734375,10.656025390625,7.08002734375,10.584025390625,7.16802734375L9.504025390625,8.480027343749999L9.488025390625,8.49602734375C9.400025390625,8.58402734375,9.256025390625,8.58402734375,9.168025390625,8.49602734375L5.584025390625,4.92802734375C5.499125390625,4.84183734375,5.361235390625,4.8383073437499995,5.272025390625,4.92002734375L2.608025390625,7.34402734375C2.560025390625,7.38402734375,2.5360253906250003,7.44802734375,2.5360253906250003,7.51202734375L2.5360253906250003,12.87202734375L13.440025390625,12.87202734375L13.440025390625,8.80802734375ZM11.168025390625,4.45602734375C11.168025390625,4.96002734375,11.576025390625,5.368027343750001,12.080025390625,5.368027343750001C12.584025390625,5.368027343750001,12.992025390625,4.96002734375,12.992025390625,4.45602734375C12.992025390625,3.95202734375,12.584025390625,3.54402734375,12.080025390625,3.54402734375C11.576025390625,3.54402734375,11.168025390625,3.95202734375,11.168025390625,4.45602734375ZM1.176025390625,1.72802734375L1.176025390625,14.23202734375L14.800025390625,14.23202734375L14.800025390625,1.72802734375L1.176025390625,1.72802734375ZM13.888025390625,13.32002734375L2.080025390625,13.32002734375L2.080025390625,2.64002734375L13.888025390625,2.64002734375L13.888025390625,13.32002734375Z" fill="#545A67" fill-opacity="1" /></g></g></svg>`
  );

export const icon_xhgkgl = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><defs><clipPath id="master_svg0_36_3471"><rect x="0" y="16" width="16" height="16" rx="0"/></clipPath></defs><g transform="matrix(1,0,0,-1,0,32)" clip-path="url(#master_svg0_36_3471)"><g><path d="M3.00382,24C3.49845,23.82364,3.97588,23.6063,4.4590499999999995,23.40856C5.53615,22.96676,6.61134,22.51962,7.68653,22.07248C7.89852,21.98341,8.10286,21.98519,8.31484,22.07248C9.46642,22.55169,10.61991,23.02733,11.7715,23.50298C12.1783,23.67043,12.5889,23.83433,12.9976,24C12.9976,23.66658,12.9978,23.33326,12.9981,23L12.4864,23C12.4224,23,12.3685,22.95484,12.3603,22.89504L12.2625,22.23236C12.1056,22.17012,11.962,22.084690000000002,11.8284,21.98706L11.1885,22.23725C11.1289,22.25983,11.0624,22.23725,11.0317,22.18233L10.51792,21.31767C10.48405,21.26276,10.50036,21.19502,10.54866,21.15779L11.0907,20.7453C11.08,20.66536,11.0731,20.58299,11.0731,20.49999C11.0731,20.41701,11.0812,20.33524,11.0907,20.25469L10.54804,19.8422C10.4991,19.80498,10.48593,19.73724,10.51729,19.68233L10.98867,18.888998C10.07373,18.604653,9.15856,18.321446,8.24418,18.0375104C8.07421,17.984067,7.90806,17.9894114,7.7381,18.0428548C6.31915,18.484653,4.9002,18.924669,3.48126,19.36647C3.20052,19.45376,3,19.69425,3,19.94722C3,21.29755,3.00191,22.64789,3.00382,24ZM14.2171,22.00447L14.8124,22.23724C14.8225,22.24082,14.8329,22.24312,14.8434,22.24419C14.9338,22.3534,14.9888,22.491509999999998,14.9905,22.64158C14.9961,23.05953,14.9954,23.47847,14.9947,23.89692L14.9947,23.89694C14.9945,24.036360000000002,14.9942,24.17573,14.9942,24.31498C14.9942,24.504179999999998,14.9939,24.693260000000002,14.9935,24.88229C14.9925,25.44919,14.9914,26.01558,14.9998,26.58298C15.0054,26.926560000000002,14.871,27.15977,14.5406,27.29863C14.1822,27.44995,13.8219,27.60305,13.4634,27.75436C11.7721,28.47,10.08075,29.1857,8.391269999999999,29.904899999999998C8.16165,30.0028,7.9395,30.0419,7.69868,29.9405C5.6153,29.0557,3.53006,28.1727,1.4448159999999999,27.28973C1.261867,27.21318,1.125589,27.09212,1.0509157,26.91232C0.9071703,26.5634,1.0751845,26.209139999999998,1.461617,26.040010000000002C2.01233,25.79969,2.57424,25.58606,3.11562,25.32615C3.59913,25.14991,4.06584,24.93272,4.53814,24.73334C5.59103,24.29184,6.64206,23.845010000000002,7.69308,23.39817C7.9003,23.30916,8.10005,23.30916,8.30726,23.39817C9.43296,23.87705,10.55865,24.35237,11.6862,24.82769C12.0839,24.99503,12.4852,25.15881,12.8847,25.324370000000002C13.1181,25.4294,13.3514,25.53265,13.6016,25.64481L13.6016,22.96776C13.6225,22.94926,13.6367,22.92397,13.6406,22.89504L13.7333,22.26753C13.7469,22.24974,13.7614,22.23272,13.7767,22.21651C13.9016,22.16304,14.0187,22.09687,14.1288,22.01897C14.1575,22.0125,14.187,22.007649999999998,14.2171,22.00447Z" fill-rule="evenodd" fill="#545A67" fill-opacity="1" /></g><g transform="matrix(1,0,0,-1,0,44.80000019073486)"><path d="M14.52824,24.59624009536743C14.53678,24.532290095367433,14.542300000000001,24.46834009536743,14.542300000000001,24.40000009536743C14.542300000000001,24.33215009536743,14.53577,24.26819009536743,14.52824,24.203760095367432L14.961829999999999,23.87376009536743C15.00098,23.84398009536743,15.01152,23.789800095367433,14.986419999999999,23.745860095367433L14.57542,23.054136095367433C14.55083,23.010205095367432,14.49512,22.994093095367433,14.44996,23.010205095367432L13.93807,23.21035009536743C13.83118,23.130294095367432,13.71576,23.06439209536743,13.5908,23.01410609536743L13.512509999999999,22.48396899536743C13.50599,22.435637995367433,13.46333,22.40000009536743,13.41164,22.40000009536743L12.58912,22.40000009536743C12.53793,22.40000009536743,12.494769999999999,22.436124695367432,12.48825,22.483964995367433L12.40996,23.014110095367432C12.2845,23.06390109536743,12.16958,23.13224909536743,12.06269,23.21035009536743L11.550801,23.01020109536743C11.503129,22.99213909536743,11.449933,23.01020109536743,11.425341,23.054140095367433L11.0143361,23.745860095367433C10.9872378,23.789800095367433,11.000286633,23.84398009536743,11.0389287,23.87376009536743L11.472521,24.203760095367432C11.463991,24.26771009536743,11.458471,24.333610095367433,11.458471,24.40000009536743C11.458471,24.46639009536743,11.464992,24.531810095367433,11.472521,24.59624009536743L11.0384283,24.926240095367433C10.999277914,24.956020095367432,10.9887428,25.01021009536743,11.0138358,25.05414009536743L11.424845,25.745860095367433C11.449433,25.789800095367433,11.505139,25.80591009536743,11.550305,25.789800095367433L12.06219,25.589650095367432C12.16908,25.66971009536743,12.2845,25.73561009536743,12.40946,25.78589009536743L12.48775,26.31604009536743C12.49427,26.36388009536743,12.53693,26.40000009536743,12.58862,26.40000009536743L13.41114,26.40000009536743C13.46233,26.40000009536743,13.50549,26.36388009536743,13.51201,26.31604009536743L13.5903,25.78589009536743C13.71576,25.736100095367433,13.830680000000001,25.66776009536743,13.93758,25.589650095367432L14.44946,25.789800095367433C14.49713,25.80786009536743,14.55033,25.789800095367433,14.57491,25.745860095367433L14.98592,25.05414009536743C15.01051,25.01021009536743,14.99998,24.956020095367432,14.96134,24.926240095367433L14.52824,24.59624009536743ZM13.000630000000001,25.100510095367433C12.60367,25.100510095367433,12.28099,24.786620095367432,12.28099,24.40049009536743C12.28099,24.014360095367433,12.60367,23.700460095367433,13.000630000000001,23.700460095367433C13.397590000000001,23.700460095367433,13.720279999999999,24.014350095367433,13.720279999999999,24.40049009536743C13.720279999999999,24.786630095367432,13.397590000000001,25.100510095367433,13.000630000000001,25.100510095367433Z" fill="#545A67" fill-opacity="1" /></g></g></svg>`
  );

export const icon_xhdtgl = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><defs><clipPath id="master_svg0_36_3451"><rect x="0" y="0" width="16" height="16" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_36_3451)"><g><path d="M0.703369140625,8.03129640625L2.698279140625,4.55259640625L6.078069140625,4.55259640625L3.984249140625,7.99831640625L6.078069140625,11.44401640625L2.698279140625,11.44401640625L0.703369140625,8.03129640625ZM6.655089140625,4.5690564062500005L4.6272291406249995,7.99829640625L6.671579140625,11.42751640625L8.699459140624999,8.01478640625L6.655089140625,4.5690564062500005ZM7.232129140625,4.5690564062500005L11.238469140625,4.55257640625L13.018969140625,7.68505640625L9.078659140625,7.68505640625L7.232129140625,4.5690564062500005ZM13.018969140625,8.31155640625L9.012699140625,8.29507640625L7.232129140625,11.42751640625L11.172469140625,11.42751640625L13.018969140625,8.31155640625ZM13.165669140625,1.11181640625L15.209069140625,4.56224640625L13.543169140625,7.50292640625L11.577169140625,3.98269640625L7.547069140625,4.10603640625L9.213009140625,1.16535030625L13.165669140625,1.11181640625ZM13.219269140625,14.87381640625L9.209139140625,14.87721640625L7.525319140625,11.94671640625L11.556169140625,12.04551640625L13.500669140625,8.51342640625L15.184469140625,11.44391640625L13.219269140625,14.87381640625Z" fill="#545A67" fill-opacity="1" /></g></g></svg>`
  );

export const icon_tzgggl = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><defs><clipPath id="master_svg0_36_3460"><rect x="0" y="0" width="16" height="16" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_36_3460)"><g><path d="M12.53880000476837,5.1683400166893L12.953900004768371,5.1683400166893C13.732500004768372,5.1683400166893,14.370000004768372,5.8058400166893005,14.368300004768372,6.5861100166893L14.368300004768372,13.668300016689301C14.368300004768372,14.4470000166893,13.730800004768371,15.0845000166893,12.952200004768372,15.0845000166893L3.0361100047683713,15.0845000166893C2.2575000047683718,15.0845000166893,1.6200000047683716,14.4470000166893,1.6200000047683716,13.668300016689301L1.6200000047683716,6.584450016689301C1.6200000047683716,5.8058400166893005,2.2575000047683718,5.1683400166893,3.0361100047683713,5.1683400166893L3.4511500047683716,5.1683400166893L7.4903100047683715,1.1308400166893005C7.491970004768372,1.1291800166893005,7.491970004768372,1.1275200166893005,7.493630004768372,1.1258590166893006C7.631430004768371,0.9880664166893005,7.812380004768372,0.9200000166893005,7.995000004768372,0.9200000166893005C8.17596000476837,0.9200000166893005,8.35857000476837,0.9880664166893005,8.496370004768371,1.1258590166893006C8.49803000476837,1.1275200166893005,8.49803000476837,1.1291800166893005,8.499690004768372,1.1308400166893005L12.53880000476837,5.1683400166893ZM7.845590004768372,2.7793800166893003L5.818540004768372,4.8064300166893C5.684060004768371,4.9392400166893005,5.778690004768372,5.1683400166893,5.967950004768372,5.1700000166893005L10.023710004768372,5.1700000166893005C10.212970004768371,5.1700000166893005,10.307600004768371,4.940900016689301,10.173130004768371,4.8064300166893L8.146070004768372,2.7793800166893003C8.06307000476837,2.6963700166893005,7.928590004768371,2.6963700166893005,7.845590004768372,2.7793800166893003ZM10.608090004768371,10.655160016689301C10.476930004768372,10.6103300166893,10.337480004768372,10.545590016689301,10.300960004768372,10.4791800166893C10.198030004768372,10.2882600166893,10.198030004768372,10.0176600166893,10.307600004768371,9.8350400166893C10.352420004768371,9.7702900166893,10.48357000476837,9.710530016689301,10.623030004768372,9.667360016689301C10.78406000476837,9.6076000166893,10.872050004768372,9.4332800166893,10.805650004768372,9.278890016689301L10.623030004768372,8.8024200166893C10.556620004768371,8.648030016689301,10.374000004768371,8.5749800166893,10.219610004768372,8.648030016689301C10.088460004768372,8.712770016689301,9.949000004768372,8.7642400166893,9.875960004768372,8.7426600166893C9.671760004768371,8.6845500166893,9.480840004768371,8.4936300166893,9.422730004768372,8.2811300166893C9.401150004768372,8.208090016689301,9.459260004768371,8.068630016689301,9.525660004768373,7.9374800166893005C9.607010004768371,7.7897300166893,9.540610004768372,7.6004700166893,9.379570004768372,7.534060016689301L8.918050004768372,7.336500016689301C8.757010004768372,7.263460016689301,8.581040004768372,7.3514500166893,8.522930004768371,7.512480016689301C8.478110004768372,7.6436300166893005,8.413360004768371,7.7830900166893,8.346950004768372,7.819610016689301C8.156040004768371,7.9225400166893,7.885430004768372,7.9225400166893,7.702810004768372,7.812970016689301C7.638070004768371,7.768150016689301,7.578300004768372,7.6369900166893006,7.535140004768372,7.4975400166893005C7.475370004768371,7.336500016689301,7.3010500047683715,7.248520016689301,7.146660004768371,7.3149200166893005L6.670200004768372,7.4975400166893005C6.515800004768372,7.563950016689301,6.442750004768372,7.7465600166893,6.515800004768372,7.9009600166893C6.580550004768371,8.032110016689302,6.632010004768372,8.1715600166893,6.6104300047683715,8.2446100166893C6.552320004768371,8.4488100166893,6.361410004768372,8.6397300166893,6.148910004768371,8.6978300166893C6.075860004768372,8.7194100166893,5.936410004768372,8.661310016689301,5.8052500047683715,8.594900016689301C5.657500004768371,8.513550016689301,5.468240004768372,8.5799600166893,5.4018400047683715,8.7410000166893L5.204280004768371,9.2025200166893C5.131230004768371,9.3635500166893,5.2192200047683714,9.5395300166893,5.380250004768372,9.5976400166893C5.511410004768372,9.6424600166893,5.650860004768371,9.7072100166893,5.687380004768372,9.7736100166893C5.790310004768371,9.9645300166893,5.790310004768371,10.2351400166893,5.680740004768372,10.4177500166893C5.635920004768372,10.4825000166893,5.504770004768371,10.5422700166893,5.3653100047683715,10.585430016689301C5.204280004768371,10.6451900166893,5.116290004768372,10.8195100166893,5.182700004768371,10.973900016689301L5.3653100047683715,11.4504000166893C5.431720004768372,11.6048000166893,5.614340004768371,11.6778000166893,5.768730004768371,11.6048000166893C5.899880004768372,11.5400000166893,6.039340004768372,11.4886000166893,6.112380004768371,11.5101000166893C6.316580004768372,11.5682000166893,6.507500004768372,11.7592000166893,6.565610004768372,11.9717000166893C6.587190004768372,12.044700016689301,6.529080004768372,12.184200016689301,6.462680004768371,12.315300016689301C6.381330004768372,12.463100016689301,6.447730004768371,12.652300016689301,6.608770004768371,12.7187000166893L7.070290004768371,12.9163000166893C7.231330004768371,12.9893000166893,7.407300004768372,12.9013000166893,7.465410004768372,12.7403000166893C7.510230004768371,12.6092000166893,7.5749800047683715,12.4697000166893,7.641390004768372,12.4332000166893C7.8323000047683715,12.3303000166893,8.102910004768372,12.3303000166893,8.285530004768372,12.4398000166893C8.350270004768372,12.484600016689301,8.410040004768373,12.6158000166893,8.453200004768371,12.7553000166893C8.512970004768372,12.9163000166893,8.687290004768371,13.004300016689301,8.84168000476837,12.9379000166893L9.318140004768372,12.7553000166893C9.472540004768373,12.688800016689301,9.545590004768371,12.5062000166893,9.472540004768373,12.351800016689301C9.407790004768373,12.220700016689301,9.35633000476837,12.0812000166893,9.37791000476837,12.008200016689301C9.436020004768372,11.8040000166893,9.626930004768372,11.6131000166893,9.83943000476837,11.5550000166893C9.912480004768371,11.533400016689301,10.051930004768371,11.5915000166893,10.183090004768372,11.6579000166893C10.33084000476837,11.739200016689301,10.520100004768372,11.672800016689301,10.586500004768371,11.5118000166893L10.78406000476837,11.0503000166893C10.857110004768371,10.8892400166893,10.769120004768372,10.7132600166893,10.608090004768371,10.655160016689301ZM7.994850004768372,11.5437000166893C8.776950004768372,11.5437000166893,9.410970004768371,10.909710016689301,9.410970004768371,10.127620016689301C9.410970004768371,9.345520016689301,8.776950004768372,8.7115000166893,7.994850004768372,8.7115000166893C7.212760004768372,8.7115000166893,6.578740004768371,9.345520016689301,6.578740004768371,10.127620016689301C6.578740004768371,10.909710016689301,7.212760004768372,11.5437000166893,7.994850004768372,11.5437000166893Z" fill-rule="evenodd" fill="#545A67" fill-opacity="1" /></g></g></svg>`
  );

export const icon_xhzlsc = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><defs><clipPath id="master_svg0_36_3445"><rect x="0" y="0" width="16" height="16" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_36_3445)"><g><path d="M8.28979,3.333L14.2997,3.333C14.6861,3.333,15,3.6319999999999997,15,4L15,13.333C15,13.7014,14.6865,14,14.2997,14L1.700315,14C1.313541,14,1,13.7014,1,13.333L1,2.667C1,2.298,1.313934,2,1.700315,2L6.89021,2L8.28979,3.333ZM8.69927,8.667L10.79916,8.667L8,6L5.19979,8.667L7.29969,8.667L7.29969,11.333L8.70032,11.333L8.70032,8.667L8.69927,8.667Z" fill="#545A67" fill-opacity="1" /></g></g></svg>`
  );

export const icon_xgljgl = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><defs><clipPath id="master_svg0_36_3505"><rect x="0" y="0" width="16" height="16" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_36_3505)"><g><path d="M6.563720703125,3.68701171875L7.999930703125,2.25080171875C9.584300703125,0.6664357187500001,12.160420703125,0.6664357187500001,13.744820703125,2.2466817187499997C15.333220703125,3.83516171875,15.333220703125,6.40717171875,13.744820703125,7.99565171875L12.308620703125,9.43187171875C11.913520703125,9.83104171875,11.271520703125,9.83104171875,10.872360703125,9.43187171875C10.473180703125,9.036801718749999,10.473180703125,8.39483171875,10.872360703125,7.99565171875L12.308620703125,6.55944171875C13.102820703125,5.76931171875,13.102820703125,4.48125171875,12.308620703125,3.68701171875C11.518420703125,2.8927717187499997,10.230390703125,2.8927717187499997,9.436150703125,3.68701171875L7.999930703125,5.12322171875C7.604870703125,5.52240171875,6.958780703125,5.51829171875,6.563720703125,5.12322171875C6.164540703125,4.72816171875,6.164540703125,4.086191718749999,6.563720703125,3.68701171875ZM7.279870703125,10.15197171875C6.884810703125,10.55114171875,6.242840703125,10.55114171875,5.843660703125,10.15197171875C5.448600703125,9.75691171875,5.448600703125,9.11493171875,5.843660703125,8.715751718749999L8.716090703125001,5.84333171875C9.111150703125,5.44415171875,9.753120703125,5.44415171875,10.152300703125,5.84333171875C10.551480703125,6.23839171875,10.551480703125,6.88036171875,10.152300703125,7.27954171875L7.279870703125,10.15197171875ZM6.563720703125,12.30841171875L7.999930703125,10.87220171875C8.394990703125,10.47713171875,9.036970703125,10.47713171875,9.440260703125,10.87631171875C9.839440703125,11.27141171875,9.839440703125,11.91331171875,9.440260703125,12.31251171875L8.004050703125,13.74871171875C6.415570703125,15.33721171875,3.843560703125,15.33721171875,2.255080703125,13.74871171875C0.6666007031250001,12.16031171875,0.6666007031250001,9.58825171875,2.255080703125,7.99977171875L3.691290703125,6.56355171875C4.0863507031249995,6.16438171875,4.728330703125,6.16438171875,5.127510703125,6.56355171875C5.522570703125,6.95862171875,5.526680703125,7.60059171875,5.127510703125,7.99977171875L3.691290703125,9.43598171875C2.897050703125,10.22610171875,2.897050703125,11.51421171875,3.691290703125,12.30841171875C4.481420703125,13.10261171875,5.769480703125,13.10261171875,6.563720703125,12.30841171875Z" fill-rule="evenodd" fill="#545A67" fill-opacity="1" /></g></g></svg>`
  );

export const icon_hygl = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><defs><clipPath id="master_svg0_36_5818"><rect x="0" y="0" width="16" height="16" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_36_5818)"><g><path d="M15,15L1,15L1,11.1454L12.5411,11.1454L14.2677,11.1674L14.2655,10.93365C14.2646,10.12522,14.077,9.32717,13.7163,8.597570000000001C13.5608,8.291070000000001,13.3678,8.00368,13.1414,7.74153C12.3174,8.49332,10.96917,8.2179,10.53128,7.20834C10.0934,6.19878,10.83612,5.07808,11.9691,5.0388C13.1021,4.99951,13.9262,6.06589,13.5644,7.10317C13.8772,7.40098,14.1374,7.74618,14.3348,8.1251C14.767,8.990020000000001,14.9925,9.93763,14.9946,10.89795L14.9967,11.1387L14.9996,11.1387L14.9996,15L15,15ZM12.0183,6.06375C11.7112,6.06394,11.4624,6.30439,11.4626,6.60084C11.4626,6.89723,11.7116,7.13747,12.0187,7.13747C12.3258,7.13741,12.5748,6.89707,12.5748,6.60061C12.5746,6.30405,12.3255,6.06375,12.0183,6.06375ZM11.9846,10.46643L1.0145007,10.46643C1.066703,9.78048,1.278775,9.33531,2.2086300000000003,9.08753L2.67591,8.97274C3.29799,8.8065,4.38807,8.42188,4.700559999999999,7.71283C4.8351500000000005,7.38589,4.8007,7.01665,4.60776,6.7182C3.9487,5.54474,3.64782,4.47837,3.71343,3.55198C3.74906,2.77623,4.11858,2.05006,4.73283,1.548729C5.24038,1.178046,5.86354,0.9852466,6.49973,1.00206828C7.46281,0.96501,8.37286,1.4281220000000001,8.885449999999999,2.2161299999999997C9.21493,2.77736,9.35293,3.42451,9.27986,4.0657499999999995C9.17361,4.99882,8.87153,5.90121,8.39206,6.71785C8.198450000000001,7.02423,8.16866,7.40162,8.31195,7.73278C8.6509,8.46948,9.81095,8.850950000000001,10.47218,9.01264L10.62552,9.04763C11.691,9.28527,11.9306,9.74758,11.9853,10.46783L11.9846,10.46643Z" fill="#545A67" fill-opacity="1" /></g></g></svg>`
  );

export const icon_jsgl = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><defs><clipPath id="master_svg0_36_5783"><rect x="0" y="0" width="16" height="16" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_36_5783)"><g><path d="M8.9999559765625,9.33333L8.9999559765625,9C8.9999559765625,8.81667,8.8499559765625,8.66667,8.6666259765625,8.66667L7.3332959765625,8.66667C7.1499559765625,8.66667,6.9999559765625,8.81667,6.9999559765625,9L6.9999559765625,9.33333C6.9999559765625,9.51667,7.1499559765625,9.66667,7.3332959765625,9.66667L7.5332959765625,9.66667L7.0132959765625,11.6067C7.0066259765625,11.6433,7.0132959765625,11.6833,7.0366259765625,11.7133L7.8399559765625,12.7867C7.9199559765625,12.8933,8.0799559765625,12.8933,8.1599559765625,12.7867L8.963295976562499,11.7133C8.9866259765625,11.6833,8.9932959765625,11.6433,8.9866259765625,11.6067L8.4666259765625,9.66667L8.6666259765625,9.66667C8.8499559765625,9.66667,8.9999559765625,9.51667,8.9999559765625,9.33333ZM7.9999559765625,8.33333C6.3499559765625,8.33333,4.9999559765625,6.98333,4.9999559765625,5.33333L4.9999559765625,4C4.9999559765625,2.35,6.3499559765625,1,7.9999559765625,1C9.649955976562499,1,10.9999559765625,2.35,10.9999559765625,4L10.9999559765625,5.33333C10.9999559765625,6.98333,9.649955976562499,8.33333,7.9999559765625,8.33333ZM12.0799259765625,9L11.3199559765625,8.83333C11.1799559765625,8.803329999999999,11.0332959765625,8.86667,10.9632959765625,8.99333L8.2066259765625,13.93C8.1166259765625,14.09,7.8832959765625,14.09,7.7932959765625,13.93L4.963295976562501,8.863330000000001C4.8935059765624995,8.7363,4.7479059765625,8.67098,4.6066259765624995,8.703330000000001L3.2532959765625,9C2.3282029765625,9.19488,1.6661884785625,10.01127,1.6666259765625,10.95667L1.6666259765625,13.3333C1.6666259765625,14.07,2.2632929765625,14.6667,2.9999559765625,14.6667L12.3333259765625,14.6667C13.0699259765625,14.6667,13.6666259765625,14.07,13.6666259765625,13.3333L13.6666259765625,10.95667C13.6666259765625,10.01,13.0033259765625,9.19333,12.0799259765625,9Z" fill="#545A67" fill-opacity="1" /></g></g></svg>`
  );

export const icon_yhgl = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="16" height="14.1975736618042" viewBox="0 0 16 14.1975736618042"><defs><clipPath id="master_svg0_36_5774"><rect x="0" y="0" width="16" height="14.1975736618042" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_36_5774)"><g><path d="M13.3339,2Q14.0924,2,14.5462,2.381773Q15,2.763547,15,3.53941L15,10.37438Q15,10.7069,14.8757,11.00246Q14.7513,11.29803,14.5337,11.5197Q14.3162,11.74138,14.024,11.87069Q13.7318,12,13.4085,12L2.56661,12Q2.24334,12,1.957371,11.87685Q1.671403,11.7537,1.4600360000000001,11.55049Q1.248668,11.34729,1.124334,11.07635Q1.000000046318,10.80542,1,10.49754L1,3.51478Q1,2.849755,1.410302,2.424878Q1.820604,2.00000137634,2.56661,2.00000137634L13.3339,2ZM4.49378,4.27833Q4.25755,4.27833,4.04618,4.36453Q3.83481,4.45074,3.67318,4.60468Q3.51155,4.7586200000000005,3.4183,4.97414Q3.32504,5.18966,3.32505,5.43596Q3.32505,5.68227,3.4183,5.89163Q3.51155,6.10099,3.67318,6.26108Q3.83481,6.42118,4.04618,6.51355Q4.25755,6.60591,4.49378,6.60591Q4.75488,6.60591,4.9724699999999995,6.51355Q5.19005,6.42118,5.34547,6.26108Q5.50089,6.10099,5.59414,5.89163Q5.68739,5.68227,5.68739,5.43596Q5.68739,4.93103,5.35169,4.60468Q5.01599,4.27833,4.49378,4.27833ZM6.44583,8.53941Q6.44583,8.30542,6.35258,8.0899Q6.25933,7.87438,6.10391,7.72044Q5.94849,7.5665,5.73091,7.47414Q5.51332,7.38177,5.27709,7.38177L3.72291,7.38177Q3.23801,7.38177,2.90231,7.72044Q2.56661,8.05911,2.56661,8.53941L2.56661,8.58867L2.56661,9.697040000000001L6.44583,9.697040000000001L6.44583,8.58867L6.44583,8.53941ZM12.2149,9.75862Q12.4636,9.75862,12.5817,9.63547Q12.6998,9.512319999999999,12.6998,9.3399Q12.6998,9.16749,12.5631,9.03818Q12.4263,8.90887,12.1776,8.90887L8.472470000000001,8.90887Q8.2238,8.90887,8.09947,9.03818Q7.97514,9.16749,7.97514,9.3399Q7.97514,9.512319999999999,8.09947,9.63547Q8.2238,9.75862,8.46004,9.75862L12.2149,9.75862ZM12.2274,8.16995Q12.476,8.16995,12.5879,8.046800000000001Q12.6998,7.92365,12.6998,7.75123Q12.6998,7.5665,12.5941,7.44335Q12.4885,7.3202,12.2398,7.3202L8.43517,7.3202Q8.186499999999999,7.3202,8.0746,7.44335Q7.9627,7.5665,7.9627,7.75123Q7.9627,7.92365,8.068380000000001,8.046800000000001Q8.17407,8.16995,8.422740000000001,8.16995L12.2274,8.16995ZM12.2398,6.60591Q12.4263,6.60591,12.5631,6.48276Q12.6998,6.35961,12.6998,6.18719Q12.6998,6.01478,12.5631,5.9101Q12.4263,5.80542,12.2398,5.80542L9.71581,5.80542Q9.52931,5.80542,9.39876,5.9101Q9.26821,6.01478,9.26821,6.18719Q9.26821,6.35961,9.39876,6.48276Q9.52931,6.60591,9.71581,6.60591L12.2398,6.60591Z" fill="#545A67" fill-opacity="1" /></g></g></svg>`
  );

export const icon_zzjggl = (props: any) =>
  iconBase(
    props,
    `<svg fill="none" version="1.1" width="16" height="16" viewBox="0 0 16 16"><defs><clipPath id="master_svg0_36_5800"><rect x="0" y="0" width="16" height="16" rx="0"/></clipPath></defs><g clip-path="url(#master_svg0_36_5800)"><g><path d="M8,2L15,4.58L15,5.48L14.1091,5.48C14.0455,5.779999999999999,13.7273,5.9,13.1545,5.9L2.7818199999999997,5.9C2.2090899999999998,5.9,1.890909,5.779999999999999,1.890909,5.48L1,5.48L1,4.58L8,2ZM1.636364,12.74L14.3636,12.74C14.6818,12.74,15,12.98,15,13.34L15,14L1,14L1,13.28C1,12.98,1.254546,12.74,1.636364,12.74ZM1.954545,12.26L1.954545,11.78C1.954545,11.6,2.1454500000000003,11.42,2.46364,11.42L2.84545,11.42L2.84545,6.32L4.690910000000001,6.32L4.690910000000001,11.42L5.64545,11.42L5.64545,6.32L7.49091,6.32L7.49091,11.42L8.381820000000001,11.42L8.381820000000001,6.32L10.29091,6.32L10.29091,11.42L11.2455,11.42L11.2455,6.32L13.0909,6.32L13.0909,11.42L13.4727,11.42C13.7909,11.42,13.9818,11.54,14.0455,11.78L14.0455,12.26L1.954545,12.26Z" fill="#545A67" fill-opacity="1" /></g></g></svg>`
  );

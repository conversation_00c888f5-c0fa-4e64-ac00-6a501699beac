import type { IMenu } from '@/views/pc-uc/menu/type';

/**
 * 菜单
 */
export const menuDataList: IMenu[] = [
  // {
  //   label: '首页',
  //   key: 'home', // 一般同路由名
  //   icon: 'sy', // 图标名
  //   routeName: 'home', // 路由名称
  // },

  {
    type: 'group',
    label: '门户网站管理',
    key: 'mhwzgl',
    children: [
      {
        label: '门户宣传图管理',
        key: 'pcPortalMgrPortalBanner',
        routeName: 'pcPortalMgrPortalBanner',
        icon: 'mhxctgl',
      },
      {
        label: '学会概况管理',
        key: 'xhgkgl',
        children: [
          {
            label: '学会简介',
            key: 'pcPortalMgrIntroduction',
            routeName: 'pcPortalMgrIntroduction',
          },
          {
            label: '组织机构',
            key: 'zzjg',
            children: [
              {
                label: '学会领导',
                key: 'pcPortalMgrLeader',
                routeName: 'pcPortalMgrLeader',
              },
              {
                label: '办事机构',
                key: 'pcPortalMgrOffice',
                routeName: 'pcPortalMgrOffice',
              },
              {
                label: '学会分支机构',
                key: 'pcPortalMgrBranch',
                routeName: 'pcPortalMgrBranch',
              },
              {
                label: '理事单位',
                key: 'pcPortalMgrUnit',
                routeName: 'pcPortalMgrUnit',
              },
            ],
          },
          {
            label: '学会章程',
            key: 'pcPortalMgrConstitution',
            routeName: 'pcPortalMgrConstitution',
          },
          // {
          //   label: '管理制度',
          //   key: 'glzd',
          // },
        ],
        icon: 'xhgkgl',
      },
      {
        label: '学会动态管理',
        key: 'pcPortalMgrNews',
        routeName: 'pcPortalMgrNews',
        icon: 'xhdtgl',
      },
      {
        label: '通知公告管理',
        key: 'pcPortalMgrNotices',
        routeName: 'pcPortalMgrNotices',
        icon: 'tzgggl',
      },
      {
        label: '学会资料上传',
        key: 'xhzlsc',
        children: [
          {
            label: '学术期刊',
            key: 'pcPortalMgrDocumentAcademicJournal',
            routeName: 'pcPortalMgrDocumentAcademicJournal',
          },
          {
            label: '研究报告',
            key: 'pcPortalMgrDocumentResearchReport',
            routeName: 'pcPortalMgrDocumentResearchReport',
          },
          {
            label: '会员申请资料',
            key: 'pcPortalMgrDocumentAcademicDatabase',
            routeName: 'pcPortalMgrDocumentAcademicDatabase',
          },
          {
            label: '培训资料',
            key: 'pcPortalMgrDocumentTeachingResources',
            routeName: 'pcPortalMgrDocumentTeachingResources',
          },
        ],
        icon: 'xhzlsc',
      },
      {
        label: '相关链接管理',
        key: 'pcPortalMgrRelateLink',
        routeName: 'pcPortalMgrRelateLink',
        icon: 'xgljgl',
      },
    ],
  },

  {
    type: 'group',
    label: '业务系统管理',
    key: 'ywxtgl',
    children: [
      {
        label: '会议管理',
        key: 'hygl',
        children: [
          {
            label: '报名签到信息管理',
            key: 'pcBizSysMgrSignInfo',
            routeName: 'pcBizSysMgrSignInfo',
          },
          {
            label: '住宿就餐信息管理',
            key: 'pcBizSysMgrHotelInfo',
            routeName: 'pcBizSysMgrHotelInfo',
          },
          {
            label: '内部人员管理',
            key: 'pcBizSysMgrPersonMgr',
            routeName: 'pcBizSysMgrPersonMgr',
          },
        ],
        icon: 'hygl',
      },
      // {
      //   label: '会员管理',
      //   key: 'hygl',
      // },
      // {
      //   label: '业务管理',
      //   key: 'ywgl',
      // },
    ],
  },

  {
    type: 'group',
    label: '基础信息管理',
    key: 'jcxxgl',
    children: [
      {
        label: '角色管理',
        key: 'pcBaseInfoMgrRole',
        routeName: 'pcBaseInfoMgrRole',
        icon: 'jsgl',
      },
      {
        label: '用户管理',
        key: 'pcBaseInfoMgrUser',
        routeName: 'pcBaseInfoMgrUser',
        icon: 'yhgl',
      },
      {
        label: '组织架构管理',
        key: 'pcBaseInfoMgrOrg',
        routeName: 'pcBaseInfoMgrOrg',
        icon: 'zzjggl',
      },
    ],
  },
];

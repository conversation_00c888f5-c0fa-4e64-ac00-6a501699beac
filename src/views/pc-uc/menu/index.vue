<template>
  <n-layout has-sider>
    <n-layout-sider
      :class="{ [$style.wrap]: true, [$style.expand]: isExpand }"
      :collapsed-width="48"
      :collapsed="isExpand"
      :width="260"
      @collapse="setExpand(false)"
      @expand="setExpand(true)"
      bordered
      collapse-mode="width"
    >
      <!--      <n-button :class="$style['btn-trigger']" @click="setExpand(!isExpand)" text>-->
      <!--        <IconExpand :class="{ [$style['icon-expand']]: true, [$style['re']]: isExpand }" />-->
      <!--      </n-button>-->

      <n-scrollbar :style="`max-height: ${scrollMaxH}; margin-top: 15px`">
        <n-menu
          v-model:value="activeKey"
          :collapsed="isExpand"
          :options="menuList"
          :inverted="isExpand"
          accordion
          @update:value="handleUpdateValue"
        />
      </n-scrollbar>
    </n-layout-sider>
  </n-layout>
</template>

<script lang="ts" setup>
// import { icon_expand as IconExpand } from './assets/icon/index';
import { useMenu } from '@/views/pc-uc/menu/useMenu';
import { computed } from 'vue';
import { useState } from '@/common/hooks/useState.ts';

const { menuList, activeKey, handleUpdateValue } = useMenu();
const [isExpand, setExpand] = useState(false);

const props = defineProps({
  headless: Boolean,
});

const scrollMaxH = computed(() => {
  return props.headless ? 'calc(100vh - 48px)' : 'calc(100vh - 64px - 48px)';
});

defineOptions({ name: 'MenuIndex' });
</script>

<style module lang="scss">
.wrap {
  //background: var(--com-box-bg);
  background: url('@/assets/login-bg.png') 0 0 no-repeat !important;
  background-size: 100% !important;

  &.expand {
    background: var(--com-primary-color);
  }
}

.btn-trigger {
  width: 100%;
  height: 48px;
  justify-content: flex-start;
  padding-left: 14px;
  font-size: 20px;

  .icon-expand {
    &.re {
      transform-origin: center center;
      transform: rotate(-180deg);
      color: #fff;
    }
  }
}
</style>

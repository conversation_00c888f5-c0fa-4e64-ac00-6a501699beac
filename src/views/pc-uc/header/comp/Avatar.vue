<template>
  <n-dropdown
    trigger="hover"
    placement="bottom-end"
    size="large"
    :show-arrow="true"
    :options="options"
    :theme-overrides="dropDownTheme()"
    @select="handleSelect"
  >
    <div :class="$style['avatar']">
      <img src="../assets/avatar.png" alt="avatar" class="mr-[5px]" />
      <n-ellipsis
        v-if="userName"
        class="!max-w-[100px] font-bold"
        :tooltip="false"
        :title="userName.length > 5 ? userName : ''"
      >
        你好，{{ userName }}
      </n-ellipsis>

      <IconDropDown class="text-[26px]" />
    </div>
  </n-dropdown>
</template>

<script lang="ts" setup>
import { $dialog } from '@/common/shareContext';
import { ACTION } from '@/views/pc-uc/header/constant.ts';
import { dropDownTheme } from '../theme/dropDownTheme';
import { MdArrowDropDown as IconDropDown } from '@kalimahapps/vue-icons';
import { ref } from 'vue';
import { useAuthPcStore } from '@/store/auth-pc';

const emits = defineEmits(['action']);

const auth = useAuthPcStore();
const userInfo = auth.userInfo;
const userName = userInfo.userName || userInfo.loginName || '--';

const options = ref([
  { label: '修改密码', key: 'changePassword' },
  { label: '退出登录', key: 'logout' },
  { label: getSysVersion(), key: 'sysVersion', disabled: true },
]);

function getSysVersion() {
  let v2 = window.$SYS_CFG.version.buildTime?.replace(/\/|\s/g, '.').replace(/^\d{4}\.|[^0-9.]/g, '');
  let ver = window.$SYS_CFG.version.version || (v2 ? `0.${v2}` : '') || '0.0.0.000000';

  // 大写V开头
  if (!/^v/i.test(ver)) {
    ver = 'V' + ver;
  } else {
    ver = ver.replace('v', 'V');
  }

  return ver;
}

function handleSelect(key: string) {
  if (key === 'logout') {
    $dialog.info({
      title: '提示',
      content: '确定退出登录吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        auth.logout();
      },
    });
  }

  if (key === 'changePassword') {
    emits('action', { action: ACTION.EDIT_PWD });
  }
}

defineOptions({ name: 'PcUcHeaderCompAvatar' });
</script>

<style module lang="scss">
.avatar {
  display: grid;
  align-items: center;
  grid-template-columns: auto 1fr auto;
  column-gap: 5px;
}
</style>

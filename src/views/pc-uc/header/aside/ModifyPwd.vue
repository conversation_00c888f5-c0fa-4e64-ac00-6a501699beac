<template>
  <n-form
    ref="formInstRef"
    :model="formData"
    :rules="rules"
    label-placement="left"
    label-width="140"
    require-mark-placement="left"
    class="pt-[30px] px-[20px]"
  >
    <n-form-item label="原密码：" path="oldPwd">
      <n-input v-model:value="formData.oldPwd" clearable />
    </n-form-item>
    <n-form-item label="新密码：" path="pwd">
      <n-input v-model:value="formData.pwd" clearable />
    </n-form-item>
    <!-- <div class="pl-[140px] text-[#f56c6c] text-[12px]">
      新密码长度为8-16位，至少包含大写字母、小写字母、数字、特殊符号中任意3项
    </div> -->
    <n-form-item label="确认新密码：" path="confirmPwd">
      <n-input v-model:value="formData.confirmPwd" clearable />
    </n-form-item>
  </n-form>
</template>

<script setup lang="ts">
import { $dialog, $toast } from '@/common/shareContext';
import { anyThreeSymbols } from './util.ts';
import { encryptMd5 } from '@/utils/crypto.ts';
import { FormInst, FormRules } from 'naive-ui';
import { ref, watch } from 'vue';
import { updatePwd } from './fetchData';
import { useAuthPcStore } from '@/store/auth-pc';

const props = defineProps({
  show: Boolean,
});

const store = useAuthPcStore();
const auth = useAuthPcStore();

const initForm = () => {
  return {
    oldPwd: '', //原密码
    pwd: '', //新密码
    confirmPwd: '', //确认密码
  };
};
//
const formInstRef = ref<FormInst | null>(null);
const formData = ref(initForm()); // 表单数据

const rules: FormRules = {
  oldPwd: {
    required: true,
    message: '请输入原密码,密码长度为8-16位',
    validator(rule, val) {
      return val.length > 7 && val.length < 17;
    },
    trigger: 'blur',
  },
  pwd: {
    required: true,
    message: '新密码长度为8-16位，至少包含大写字母、小写字母、数字、特殊符号中任意3项',
    validator(rule, val) {
      return rulesFunOldPwd(val);
    },
    trigger: 'blur',
  },
  confirmPwd: { required: true, validator: rulesFunPwd, message: '两次密码输入不一致', trigger: 'blur' },
};

function handleSubmit() {
  return new Promise((resolve, reject) => {
    formInstRef.value?.validate(async (errors) => {
      if (!errors) {
        const params = {
          userId: store.userInfo.id,
          oldPassword: encryptMd5(`${formData.value.oldPwd}true`), // 原密码
          newPassword: formData.value.pwd, // 新密码
          // confirmPwd: formData.value.confirmPwd, // 确认密码
        };
        updatePwd(params)
          .then(() => {
            resolve('submitted');
            reset();

            $dialog.success({
              title: '提示',
              content: '密码已修改，请重新登录账号！',
              positiveText: '确定',
              closeOnEsc: false,
              closable: false,
              maskClosable: false,
              onPositiveClick: () => {
                auth.logout();
              },
            });
          })
          .catch(reject);
      } else {
        if (errors.length) {
          try {
            const first = errors[0][0];
            if (first.message) {
              $toast.error(first.message);
            }
          } catch (e) {}
        }
        reject(errors);
      }
    });
  });
}

function reset() {
  formData.value = initForm();
}

function rulesFunPwd() {
  if (formData.value.pwd !== formData.value.confirmPwd) return false;
}

function rulesFunOldPwd(val: string) {
  if (val) {
    if (val.length > 7 && val.length < 17) {
      return anyThreeSymbols(val);
    } else {
      return false;
    }
  } else {
    return false;
  }
}

// on show
watch(
  () => props.show,
  (val) => {
    if (!val) {
      reset();
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});

defineOptions({ name: 'PcUcHeaderAsideModifyPwd' });
</script>

<style module lang="scss">
.border-card {
  @apply mb-[15px] border border-solid border-[#E0E0E6] rounded-[3px];
}
</style>

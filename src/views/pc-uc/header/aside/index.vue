<template>
  <ComDrawerB
    :autoFocus="false"
    :footerPaddingBottom="25"
    :maskClosable="false"
    :show-action="isModifyPwd"
    :positive-loading="submitLoading"
    :closeOnPositive="false"
    @handle-negative="handleClose"
    @handle-positive="handleSubmit"
    class="!w-[430px]"
  >
    <ModifyPwd ref="modifyPwdRef" :show="show" />
  </ComDrawerB>
</template>

<script lang="ts" setup>
import ComDrawerB from '@/components/drawer/ComDrawerB.vue';
import ModifyPwd from './ModifyPwd.vue';
import { computed, useAttrs, inject, Ref, ref } from 'vue';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { ACTION, IActionData, PROVIDE_KEY } from '../constant.ts';

const emits = defineEmits(['action']);
const attrs = useAttrs();
const show = computed(() => !!attrs.show);
const currentAction = inject(PROVIDE_KEY.currentAction) as Ref<IActionData>; // inject
const isModifyPwd = computed(() => currentAction.value.action === ACTION.EDIT_PWD);
const modifyPwdRef = ref();
const [submitLoading, wrapFn] = useAutoLoading(false, 0);

function handleSubmit() {
  wrapFn(modifyPwdRef.value?.handleSubmit())
    .then(() => {
      handleSubmitted();
    })
    .catch(() => {});
}

function handleSubmitted() {
  handleClose();
}

function handleClose() {
  emits('update:show' as any, false); // any-屏蔽组件内透传的emit类型
}

defineOptions({ name: 'PcUcHeaderAsideIndex' });
</script>

<style module lang="scss"></style>

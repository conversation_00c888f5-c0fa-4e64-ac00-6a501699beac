<template>
  <div class="error-container">
    <div class="error-content">
      <h1 class="error-title">403</h1>
      <p class="error-subtitle">页面未找到</p>
      <p class="error-description">暂无使用权限，请联系本系统的管理员开设！</p>

      <div class="error-actions">
        <button class="btn-primary" @click="goHome">返回首页</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const goHome = () => {
  router.push('/');
};

defineOptions({ name: 'ErrorPage403' });
</script>

<style scoped>
.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.error-content {
  text-align: center;
  max-width: 400px;
  width: 100%;
}

.error-icon svg {
  width: 100%;
  height: 100%;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.error-title {
  font-size: 6rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 0.5rem 0;
  line-height: 1;
}

.error-subtitle {
  font-size: 1.5rem;
  color: #34495e;
  margin: 0 0 1rem 0;
  font-weight: 500;
}

.error-description {
  font-size: 1rem;
  color: #7f8c8d;
  margin: 0 0 2rem 0;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-primary {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.btn-primary {
  background: #3498db;
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

@media (max-width: 480px) {
  .error-container {
    padding: 1rem;
  }

  .error-title {
    font-size: 4rem;
  }

  .error-subtitle {
    font-size: 1.25rem;
  }

  .error-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-primary {
    width: 100%;
    max-width: 200px;
  }
}
</style>

<template>
  <div class="contentBox6">
    <img src="../assets/bg4.png" />
    <ComPortalBox title="分支机构" :has-more="false"> <Table></Table></ComPortalBox>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ComPortalBox from '@/views/pc-portal/common/comp/ComPortalBox.vue';
import Table from './table/index.vue';

defineOptions({ name: 'contentBox6Index' });
</script>

<style scoped lang="scss">
.contentBox6 {
  width: 100%;
  height: 440px;
  // display: flex;
  padding-top: 32px;
  padding-left: 15px;
  padding-right: 15px;
  padding-bottom: 15px;
  justify-content: space-between;
  background: linear-gradient(to bottom, #e6f3ff, #ffffff);
  border-radius: 8px;
  // background-color: black;
  position: relative;
  img {
    position: absolute;
    left: 0;
    top: 0;
  }
}
</style>

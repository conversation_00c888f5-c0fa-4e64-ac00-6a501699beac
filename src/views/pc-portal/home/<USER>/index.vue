<template>
  <ComPortalBox :has-more="false" title="相关链接">
    <div class="left">
      <img src="../assets/bg1.png" />
      <ComPortalBox title="政府机构" :has-more="false"><Table></Table> </ComPortalBox>
    </div>
    <div class="contentBox5">
      <div class="main">
        <img src="../assets/bg2.png" />
        <ComPortalBox title="支撑单位" :has-more="false"><Table2 /> </ComPortalBox>
      </div>
      <div class="rigth">
        <img src="../assets/bg3.png" />
        <ComPortalBox title="会员单位" :has-more="false"><Table3></Table3> </ComPortalBox>
      </div>
    </div>
  </ComPortalBox>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ComPortalBox from '@/views/pc-portal/common/comp/ComPortalBox.vue';
import Table from './table/index.vue';
import Table2 from './table/index2.vue';
import Table3 from './table/index3.vue';

const tableData = ref([
  { id: 1, name: '教育部' },
  {
    id: 2,
    name: '科技部',
  },
  {
    id: 3,
    name: '民政部',
  },
  {
    id: 4,
    name: '住建部',
  },
  {
    id: 5,
    name: '应急管理部',
  },
]);
defineOptions({ name: 'contentBox5Index' });
</script>

<style scoped lang="scss">
.left {
  width: 100%;
  height: 220px;
  background: linear-gradient(to left, #e6f3ff, #ffffff);
  border-radius: 8px;
  padding-top: 32px;
  padding-left: 15px;
  padding-right: 15px;
  padding-bottom: 15px;
  position: relative;
  img {
    width: 400px;
    position: absolute;
    // left: 0;
    right: 0;
    top: 0;
  }
}
.contentBox5 {
  width: 100%;
  height: 400px;
  margin-top: 48px;
  display: flex;
  justify-content: space-between;

  .main {
    width: 49%;
    height: 100%;
    background: linear-gradient(to left, #e6f3ff, #ffffff);
    border-radius: 8px;
    padding-top: 32px;
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 15px;
    position: relative;
    img {
      width: 400px;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
  .rigth {
    width: 49%;
    height: 100%;
    background: linear-gradient(to left, #e6f3ff, #ffffff);
    border-radius: 8px;
    padding-top: 32px;
    padding-left: 15px;
    padding-right: 15px;
    padding-bottom: 15px;
    position: relative;
    img {
      width: 400px;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
}
</style>

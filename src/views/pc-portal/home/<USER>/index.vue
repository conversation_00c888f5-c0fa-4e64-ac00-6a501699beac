<template>
  <ComPortalBox title="学会动态" moreRouteName="pcPortalLearnDynamicIndex">
    <div class="contentBox1">
      <div v-for="item in listData" :key="item.id" class="contentBox1_list" @click="handleClick(item)">
        <div class="header_img">
          <div
            :style="{
              width: '100%',
              height: '266px',

              background: `url('${getPahtUrl(item.fmtFileAttachmentList[0]?.filePath || '')}') center 0 no-repeat`,
              'background-size': 'cover',
            }"
            alt="动态图片"
          ></div>
        </div>
        <div class="content">
          <p>{{ item.fbsj }} · {{ item.lxName }}</p>
          <p>{{ item.bt }}</p>
          <div class="lineClamp" v-html="processHtmlContent(item.zw)"></div>
        </div>
        <div class="content_bottom">
          <span>阅读全文</span>
          <img src="../assets/jiantou.png" alt="箭头图标" />
        </div>
      </div></div
  ></ComPortalBox>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import ComPortalBox from '@/views/pc-portal/common/comp/ComPortalBox.vue';
import { showNewsAPI } from './fetchData';

const router = useRouter();
const listData: any = ref([]);
function handleClick(item: any) {
  router.push({
    name: 'pcPortalLearnDynamicDetails',
    query: {
      id: item.id,
    },
  });
}
// 处理 HTML 内容
function processHtmlContent(html: any) {
  if (!html) return '';
  // 创建临时 div 来处理 HTML 字符串
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  // 查找所有图片元素，并隐藏
  const images = tempDiv.querySelectorAll('img');
  images.forEach((img) => {
    img.style.display = 'none';
  });
  return tempDiv.innerHTML;
}
// 图片地址
function getPahtUrl(val: string) {
  return window.$SYS_CFG.apiBaseFile + val;
}
const getListData = () => {
  showNewsAPI({ pageNo: 1, pageSize: 3 }).then((res) => {
    listData.value = res.data.rows || [];
  });
};
getListData();
defineOptions({ name: 'contentBox1Index' });
</script>

<style scoped lang="scss">
.contentBox1 {
  width: 100%;
  height: 440px;
  display: flex;
  justify-content: space-between;
  .contentBox1_list {
    width: 448px;
    height: 440px;
    border-radius: 8px;
    background-color: white;
    position: relative;
    cursor: pointer;
    .content {
      padding: 10px;
      p:nth-child(1) {
        font-size: 14px;
        color: #999;
      }
      p:nth-child(2) {
        margin-top: 10px;

        font-weight: 600;
        font-size: 18px;
        color: #000000;
      }
      .lineClamp {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3; /* 限制显示3行 */
        line-clamp: 3; /* 限制显示3行 */
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 1.5;
      }
    }
    .content_bottom {
      bottom: 0;
      position: absolute;
      padding: 10px;

      display: flex;

      span {
        font-weight: 400;
        font-size: 16px;
        color: #0c87e5;
      }
      img {
        width: 20px;
        height: 20px;
        margin-top: 2px;
        margin-left: 10px;
      }
    }
  }
}
</style>

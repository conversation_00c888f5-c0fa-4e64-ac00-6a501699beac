<template>
  <ComPortalBox :has-more="false">
    <div class="contentBox2">
      <div class="left">
        <ComPortalBox @moreClick="moreClick">
          <template #title>
            <template v-for="item in spanList" :key="item.name">
              <span :class="activeIndex == item.name ? 'active' : 'sp'" @click="handleClick(item.name)">{{
                item.label
              }}</span>
            </template>
          </template>
          <Table :tableData="tableData"></Table>
        </ComPortalBox>
      </div>
      <div class="rigth">
        <ComPortalBox title="功能服务" :has-more="false">
          <Card></Card>
        </ComPortalBox>
      </div>
    </div>
  </ComPortalBox>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import ComPortalBox from '@/views/pc-portal/common/comp/ComPortalBox.vue';
import Table from './table/index.vue';
import Card from './table/card.vue';
import { showNoticesAPI, queryDictDataByTypeAPI } from './fetchData';

const router = useRouter();

const tableData: any = ref([]);
const activeIndex: any = ref('0');
const spanList: any = ref([]);

const moreClick = () => {
  router.push({ name: 'pcPortalNoticeIndex', query: { type: activeIndex.value } });
};
function getTypeList() {
  queryDictDataByTypeAPI({ type: 'tzlx' }).then((res) => {
    spanList.value = res.data.map((item: any) => ({
      name: item.dictValue,
      label: item.dictLabel,
    }));
  });
}
const getTable = (val: any) => {
  showNoticesAPI({ pageNo: 1, pageSize: -1, xslx: val }).then((res) => {
    tableData.value = res.data.rows || [];
  });
};
const handleClick = (index: number) => {
  activeIndex.value = index;
  getTable(index);
};

getTable(activeIndex.value);
getTypeList();

defineOptions({ name: 'contentBox2Index' });
</script>

<style scoped lang="scss">
.contentBox2 {
  width: 100%;

  display: flex;
  justify-content: space-between;
  .left {
    width: 49%;
    height: 478px;
    padding: 24px;
    background-color: white;
    .sp {
      display: inline-block;
      height: 50px;
      font-weight: 700;
      text-align: center;
      line-height: 50px;
      font-size: 25px;
      color: #333333;
      margin-right: 20px;
    }
    .active {
      display: inline-block;
      height: 50px;
      font-weight: 700;
      text-align: center;
      line-height: 50px;
      font-size: 26px;
      color: #0c87e5;
      margin-right: 20px;
      border-bottom: 3px solid #0c87e5;
    }
  }
  .rigth {
    width: 49%;
    height: 478px;
    padding: 24px;
    background-color: white;
  }
}
</style>

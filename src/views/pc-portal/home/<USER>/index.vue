<template>
  <div :class="$style.PcPortalHomeBannerIndex">
    <n-carousel draggable autoplay :show-arrow="!!dataList.length">
      <img
        :class="[$style.carouselImg, item.link ? 'cursor-pointer' : '']"
        v-for="item of dataList"
        :key="item.src"
        :src="item.src"
        alt="轮播图"
        @click="handleImgClick(item.link)"
      />

      <template #arrow="{ currentIndex }">
        <div :class="$style.carouselTitle" v-if="dataList[currentIndex].title">
          {{ dataList[currentIndex].title }}
        </div>
      </template>

      <template #dots="{ total, currentIndex, to }">
        <ul :class="$style.customDots">
          <li
            v-for="index of total"
            :key="index"
            :class="{ [$style['is-active']]: currentIndex === index - 1 }"
            @click="to(index - 1)"
          />
        </ul>
      </template>
    </n-carousel>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getBannerList } from '@/views/pc-portal/home/<USER>/fetchData.ts';
import { BannerPageVo } from './type.ts';
import { useRouter } from 'vue-router';

interface IDataItem {
  title: string;
  src: string;
  link: string;
}

const router = useRouter();
const dataList = ref<IDataItem[]>([]);

function getData() {
  getBannerList().then((res) => {
    dataList.value = processData(res.data || []);
  });
}

function processData(data: BannerPageVo[]) {
  const arr: IDataItem[] = [];

  for (const item of data) {
    const filePath = item?.fileAttachmentList?.[0].filePath;

    if (filePath) {
      arr.push({
        title: item.bt,
        link: item.lj,
        src: getFileURL(filePath),
      });
    }
  }

  return arr;
}

function getFileURL(filePath: string) {
  return window.$SYS_CFG.apiBaseFile + filePath;
}

/**
 * 图片点击
 * @param link 支持 路由路径 以及 http链接
 */
function handleImgClick(link: string) {
  if (link) {
    if (link.startsWith('http')) {
      window.open(link, link);
    } else {
      router.push({ path: link });
    }
  }
}

// on created
getData();

defineOptions({ name: 'PcPortalHomeBannerIndex' });
</script>

<style module lang="scss">
.PcPortalHomeBannerIndex {
  width: 100%;
  aspect-ratio: 32/9;
  background: #000;

  .carouselImg {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .carouselTitle {
    display: flex;
    position: absolute;
    bottom: 8%;
    left: 5%;
    color: #fff;
    max-width: 95%;
    padding: 0 1%;
    border-radius: 2%;
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(1px);
    font-size: 36px;
    font-weight: 700;
  }

  .customDots {
    display: flex;
    margin: 0;
    padding: 0;
    position: absolute;
    bottom: 4%;
    left: 50%;
    transform: translateX(-50%);
  }

  .customDots li {
    display: inline-block;
    width: 12px;
    height: 4px;
    margin: 0 3px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.4);
    transition:
      width 0.3s,
      background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
  }

  .customDots li.is-active {
    width: 40px;
    background: rgba(255, 255, 255, 0.8);
  }
}
</style>

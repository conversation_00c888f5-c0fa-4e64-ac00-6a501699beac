<template>
  <div>
    <Banner />

    <div class="com-pc-portal-container">
      <Xhdt />
      <Xhtz />
      <Jqhy class="!mb-[48px] !mt-[48px]" />
      <Zlxz class="!mt-[48px]" />
      <Zfjg class="!mt-[80px]" />
      <Fzjg class="!mt-[48px]" />
      <br />
      <br />
    </div>
  </div>
</template>

<script setup lang="ts">
import Banner from './banner/index.vue';
import Xhdt from './xhdt/index.vue';
import Xhtz from './xhtz/index.vue';
import Jqhy from './jqhy/index.vue';
import Zlxz from './zlxz/index.vue';
import Zfjg from './zfjg/index.vue';
import Fzjg from './fzjg/index.vue';
defineOptions({ name: 'PcPortalHomeIndex' });
</script>

<style scoped lang="scss"></style>

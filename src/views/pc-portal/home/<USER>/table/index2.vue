<template>
  <div class="list">
    <n-scrollbar style="height: calc(478px - 206px)" v-if="tableData.length > 0">
      <div class="li" v-for="item in tableData" :key="item.id" @click="toUrl(item.ljdz)">{{ item.bt || '--' }}</div>
    </n-scrollbar>
    <ComEmpty v-else />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { showRelateLinkAPI } from '../fetchData';
import { $toast } from '@/common/shareContext';
import ComEmpty from '@/components/empty/index.vue';

const tableData: any = ref([]);
const getTable = () => {
  showRelateLinkAPI({ pageNo: 1, pageSize: -1, xslx: 1 }).then((res) => {
    tableData.value = res.data.rows || [];
  });
};
getTable();
const toUrl = (url: string) => {
  console.log('跳转链接:', url);
  if (url) {
    window.open(url);
  } else {
    $toast.error('链接地址为空');
  }
};
defineOptions({ name: 'contentBox5TableIndex' });
</script>

<style scoped lang="scss">
.list {
  width: 100%;

  // display: flex;
  // justify-content: space-around;
  .li {
    cursor: pointer;
    // height: 80px;
    width: 98%;
    padding-left: 10px;
    padding-right: 10px;
    height: 50px;
    margin-bottom: 20px;
    line-height: 50px;
    border-radius: 6px;
    background-color: rgb(226, 237, 254);
    color: rgb(47, 148, 232);
  }
}
</style>

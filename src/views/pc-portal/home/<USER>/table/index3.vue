<template>
  <!-- 不超过12条时显示静态列表 -->
  <div v-if="total > 0 && total <= 12" class="static-list">
    <div class="li" v-for="item in tableData" :key="item.id">
      {{ item.dwmc || '--' }}
    </div>
  </div>

  <!-- 超过12条时显示轮播 -->
  <n-carousel
    v-else-if="total > 12"
    style="height: 303px"
    :space-between="20"
    draggable
    :on-update:current-index="handlePageChange"
    :show-dots="total > 12"
  >
    <n-carousel-item v-for="(page, index) in paginatedData" :key="index">
      <div class="list">
        <div class="li" v-for="(item, idx) in page" :key="item.id || `placeholder-${index}-${idx}`">
          {{ item.dwmc || '--' }}
        </div>
      </div>
    </n-carousel-item>
  </n-carousel>
  <ComEmpty v-else />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { NCarousel, NCarouselItem } from 'naive-ui';
import { showOrgUnitPageListAPI } from '../fetchData';
import ComEmpty from '@/components/empty/index.vue';

interface LinkItem {
  id: number | string;
  bt: string;
  [key: string]: any;
}

const tableData = ref<LinkItem[]>([]);
const total = ref(0);
const currentPage = ref(1);
const PAGE_SIZE = 12;
const loadedPages = ref<Record<number, LinkItem[]>>({});

// 加载指定页数据
const loadPageData = async (pageNo: number) => {
  try {
    if (!loadedPages.value[pageNo]) {
      const res = await showOrgUnitPageListAPI({ pageNo, pageSize: PAGE_SIZE });
      console.log('API3响应:', res);

      if (res?.data) {
        loadedPages.value[pageNo] = res.data.rows || [];
        total.value = res.data.total || 0;
      }
    }
    currentPage.value = pageNo;
    tableData.value = loadedPages.value[pageNo] || [];
  } catch (error) {
    console.error('加载数据失败:', error);
  }
};

// 计算分页数据
const paginatedData = computed(() => {
  const pageCount = Math.ceil(total.value / PAGE_SIZE);
  return Array.from({ length: pageCount }, (_, i) => {
    const pageNo = i + 1;
    if (loadedPages.value[pageNo]) {
      return loadedPages.value[pageNo];
    }

    // 计算当前页应有的数据量
    const remainingItems = total.value - i * PAGE_SIZE;
    const currentPageSize = Math.min(PAGE_SIZE, remainingItems);

    return Array(currentPageSize).fill({});
  });
});

// 处理轮播切换
const handlePageChange = (index: number) => {
  const pageNo = index + 1;
  if (pageNo !== currentPage.value) {
    loadPageData(pageNo);
  }

  // 预加载下一页
  if (pageNo * PAGE_SIZE < total.value) {
    loadPageData(pageNo + 1);
  }
};

// 初始化加载第一页
onMounted(() => {
  loadPageData(1);
});

defineOptions({ name: 'contentBox5TableIndex' });
</script>

<style scoped lang="scss">
.static-list,
.list {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  .li {
    cursor: pointer;
    width: 100%; // 每行4个
    padding: 0 10px;
    height: 50px;
    line-height: 50px;
    border-radius: 6px;
    background-color: rgb(226, 237, 254);
    color: rgb(47, 148, 232);

    box-sizing: border-box;

    @media (max-width: 1200px) {
      width: calc(33.33% - 15px); // 小屏幕每行3个
    }

    @media (max-width: 768px) {
      width: calc(50% - 15px); // 移动端每行2个
    }
  }
}

/* 自定义轮播指示点样式 */
:deep(.n-carousel__dots) {
  bottom: -10px;

  .n-carousel__dot {
    background: rgba(47, 148, 232, 0.3);
    width: 10px;
    height: 10px;

    &--active {
      background: rgb(47, 148, 232);
      width: 20px;
    }
  }
}
</style>

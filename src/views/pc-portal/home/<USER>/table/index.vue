<template>
  <!-- 不超过12条时显示静态列表 -->
  <div v-if="total > 0 && total <= 12" style="height: 343px; width: 1400px">
    <div class="list">
      <div class="li" v-for="item in tableData" :key="item.id">
        {{ item.jgmc }}
      </div>
    </div>
  </div>

  <!-- 超过12条时显示轮播 -->
  <n-carousel
    v-else-if="total > 12"
    style="height: 343px; width: 1400px"
    draggable
    :on-update:current-index="handlePageChange"
    :show-dots="true"
  >
    <n-carousel-item v-for="(page, index) in paginatedData" :key="index">
      <div class="list">
        <div class="li" v-for="(item, idx) in page" :key="item.id ? item.id : `placeholder-${index}-${idx}`">
          {{ item.jgmc || (item.__loading ? '加载中...' : '') }}
        </div>
      </div>
    </n-carousel-item>
  </n-carousel>

  <!-- 无数据提示 -->
  <ComEmpty v-else />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { NCarousel, NCarouselItem } from 'naive-ui';
import { showOrgBranchAPI } from '../fetchData';
import ComEmpty from '@/components/empty/index.vue';

interface OrgItem {
  id: number | string;
  jgmc: string;
  __loading?: boolean;
  [key: string]: any;
}

const tableData = ref<OrgItem[]>([]);
const total = ref(0);
const currentPage = ref(1);
const loading = ref(true); // 新增加载状态
const PAGE_SIZE = 12;
const loadedPages = ref<Record<number, OrgItem[]>>({});

// 加载指定页数据
const loadPageData = async (pageNo: number) => {
  try {
    loading.value = true;
    if (!loadedPages.value[pageNo]) {
      const res = await showOrgBranchAPI({ pageNo, pageSize: PAGE_SIZE });
      console.log('API响应:', res); // 调试日志

      if (res?.data) {
        loadedPages.value[pageNo] = (res.data.rows || []).map((item: any) => ({
          ...item,
          id: item.id || `${pageNo}-${Date.now()}`, // 确保有唯一id
        }));
        total.value = res.data.total || 0;
      }
    }
    currentPage.value = pageNo;
    tableData.value = loadedPages.value[pageNo] || [];
  } catch (error) {
    console.error('加载数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 计算分页数据
const paginatedData = computed(() => {
  const pageCount = Math.max(1, Math.ceil(total.value / PAGE_SIZE)); // 确保至少1页

  return Array.from({ length: pageCount }, (_, i) => {
    const pageNo = i + 1;
    if (loadedPages.value[pageNo]) {
      return loadedPages.value[pageNo];
    }

    // 计算当前页应有的数据量
    const remainingItems = Math.max(0, total.value - i * PAGE_SIZE);
    const currentPageSize = Math.min(PAGE_SIZE, remainingItems);

    return Array(currentPageSize).fill({ __loading: true });
  });
});

// 处理轮播切换
const handlePageChange = (index: number) => {
  const pageNo = index + 1;
  if (pageNo !== currentPage.value) {
    loadPageData(pageNo);
  }

  // 预加载下一页
  if (pageNo * PAGE_SIZE < total.value) {
    loadPageData(pageNo + 1);
  }
};

// 初始化加载第一页
onMounted(() => {
  loadPageData(1);
});

defineOptions({ name: 'contentBox6Index' });
</script>

<style scoped lang="scss">
.list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  .li {
    width: 323px;
    height: 56px;
    line-height: 56px;
    text-align: center;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #d8e4f0;
  }
}

.loading,
.no-data {
  height: 343px;
  width: 1400px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #666;
}

/* 自定义轮播指示点样式 */
:deep(.n-carousel__dots) {
  .n-carousel__dot {
    background: rgba(0, 0, 0, 0.2);

    &--active {
      background: #18a058;
    }
  }
}
</style>

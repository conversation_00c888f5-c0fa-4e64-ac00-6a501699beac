<template>
  <n-scrollbar style="height: calc(478px - 110px)" v-if="tableData.length > 0">
    <div class="tableDataDom" v-for="(item, index) in tableData" :key="index" @click="toUrl(item.id)">
      <div class="left">
        <img src="../../assets/xiaoxi.png" />
      </div>
      <div class="content">
        <p>{{ item.bt }}</p>
        <p>{{ item.tzlxName }}</p>
      </div>
      <div class="right">
        <img src="../../assets/jiantou2.png" />
      </div>
    </div>
  </n-scrollbar>
  <ComEmpty v-else class="!mt-[60px]" />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import type { PropType } from 'vue';
import ComEmpty from '@/components/empty/index.vue';

const router = useRouter();

interface TableItem {
  id: string;
  bt: string;
  tzlxName: string;
}
const props = defineProps({
  tableData: {
    type: Array as PropType<TableItem[]>,
    default: () => [],
  },
});
const toUrl = (id: string) => {
  router.push({ name: 'pcPortalNoticeDetails', query: { id } });
};

defineOptions({ name: 'contentBox2TableIndex' });
</script>

<style scoped lang="scss">
.tableDataDom {
  width: 98%;
  margin-top: 10px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  // align-items: center;
  // padding: 10px;
  padding-bottom: 10px;
  // border-bottom: 1px solid #e0e0e0;

  .left {
    img {
      width: 42px;
      height: 42px;
    }
  }
  .content {
    flex: 1;
    margin: 0 10px;

    p:nth-child(1) {
      font-weight: 600;
      font-size: 18px;
      color: #000000;
      line-height: 25px;
    }
    p:nth-child(2) {
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      margin-top: 10px;
      line-height: 20px;
    }
  }
  .right {
    img {
      width: 24px;
      height: 24px;
    }
  }
}
</style>

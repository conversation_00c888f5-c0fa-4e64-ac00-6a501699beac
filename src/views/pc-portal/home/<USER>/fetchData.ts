import { api } from '@/api';
import { $http } from '@tanzerfe/http';

// 获取学会动态列表
export function showRelateLinkAPI(query: any) {
  const url = api.getUrl(api.type.portal, api.name.jzy.showRelateLink, query);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...query } });
}
export function showOrgUnitPageListAPI(query: any) {
  const url = api.getUrl(api.type.portal, api.name.jzy.showOrgUnitPageList);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...query } });
}

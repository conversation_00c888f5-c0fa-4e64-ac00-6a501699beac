<template>
  <div :class="$style.PcPortalSearch" class="com-pc-portal-container">
    <ComBread :data="breadData" />

    <div v-if="dataList.length">
      <div>
        <section v-for="item in dataList" :key="item.id" :class="$style.group" @click="handleClick(item)">
          <div :class="$style.title">
            {{ item.bt }}
          </div>
          <div :class="$style.time">{{ item.fbsj }}</div>
        </section>
      </div>
      <div class="flex justify-end mt-[24px]">
        <PaginationComp />
      </div>
    </div>
    <ComEmpty v-else class="com-pc-portal-empty" />
  </div>
</template>

<script setup lang="ts">
import ComBread from '@/components/breadcrumb/ComBread.vue';
import ComEmpty from '@/components/empty/index.vue';
import type { IBreadData } from '@/types';
import { computed, ref, watch } from 'vue';
import { IPageItem } from './type.ts';
import { pageList } from './fetchData.ts';
import { useNaivePagination, useNPaginationComp } from '@/utils/useNaivePagination.ts';
import { useRouter } from 'vue-router';

const breadData: IBreadData[] = [{ name: '首页', routeRaw: { path: '/' }, clickable: true }, { name: '搜索结果' }];
const { pagination, updateTotal } = useNaivePagination(getData, { pageSize: 6 });
const PaginationComp = useNPaginationComp(pagination);

const router = useRouter();
const keyword = computed(() => router.currentRoute.value.query.keyword as string);
const timestamp = computed(() => router.currentRoute.value.query.timestamp as string);
const dataList = ref<IPageItem[]>([]);

function handleClick(val: IPageItem) {
  // news/detail?id=1 截取参数传给query
  if (val.redirectUrl) {
    const [path, queryString] = val.redirectUrl.split('?');

    if (queryString) {
      // 解析查询参数
      const params = new URLSearchParams(queryString);
      const queryObj: Record<string, string> = {};

      // 将 URLSearchParams 转换为普通对象
      params.forEach((value, key) => {
        queryObj[key] = value;
      });

      // 使用路径和查询参数进行路由跳转
      router.push({
        path: path,
        query: queryObj,
      });
    } else {
      // 如果没有查询参数，直接跳转路径
      router.push({ path: path });
    }
  }
}

function getData() {
  pageList({
    pageNo: pagination.page,
    pageSize: pagination.pageSize,
    keyWord: keyword.value,
  }).then((res) => {
    dataList.value = res.data.rows || [];
    updateTotal(res.data.total || 0);
  });
}

function reset() {
  dataList.value = [];
  pagination.page = 1;
  updateTotal(0);
}

watch(
  () => [keyword.value, timestamp.value],
  ([val]) => {
    reset();

    if (val) {
      getData();
    }
  },
  { immediate: true }
);

defineOptions({ name: 'PcPortalSearch' });
</script>

<style module lang="scss">
.PcPortalSearch {
  min-height: 400px;

  .group {
    background: #fff;
    padding: 24px;
    border-radius: 8px;
    margin: 16px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    column-gap: 20px;
    cursor: pointer;
  }

  .title {
    font-size: 18px;
    font-weight: 600;
  }

  .time {
    font-size: 16px;
    color: #666;
    flex-shrink: 0;
  }
}
</style>

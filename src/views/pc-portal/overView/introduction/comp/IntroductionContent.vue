<template>
  <div :class="$style.introductionContent">
    <div v-if="Object.keys(props.introductionData).length">
      <h2 :class="$style.title">{{ props.introductionData?.bt }}</h2>

      <div :class="$style.mainContent">
        <!-- 文字内容区域 -->
        <div :class="$style.textContent">
          <div :class="$style.logoSection">
            <div :class="$style.logoContainer">
              <img v-if="logoImageUrl" :src="logoImageUrl" alt="" :class="$style.logo" />
            </div>
          </div>

          <div :class="$style.description" v-html="props.introductionData?.zw"></div>
        </div>
      </div>
    </div>
    <div class="text-center com-pc-portal-container" v-else>
      <ComEmpty />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { PortalIntroductionVo } from '../type';
import ComEmpty from '@/components/empty/index.vue';

interface Props {
  introductionData?: PortalIntroductionVo;
}

const props = withDefaults(defineProps<Props>(), {
  introductionData: () => ({}),
});

// 计算图片URL
const logoImageUrl = computed(() => {
  const fileList = props.introductionData?.xctFileAttachmentList;
  if (fileList && fileList.length > 0) {
    const firstFile = fileList[0];
    if (firstFile.filePath) {
      return (window as any).$SYS_CFG?.apiBaseFile + firstFile.filePath;
    }
  }
  return '';
});

defineOptions({ name: 'IntroductionContent' });
</script>

<style module lang="scss">
.introductionContent {
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 24px;
  }

  .mainContent {
    display: block;
  }

  .textContent {
    .logoSection {
      float: right;
      margin-left: 20px;
      margin-bottom: 20px;
      background: #fff;

      .logoContainer {
        width: 486px;
        height: 320px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .logoWrapper {
        width: 200px;
        height: 200px;
        margin: 0 auto 1rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .logo {
        max-width: 400px;
        object-fit: contain;
      }

      .logoTitle {
        font-size: 16px;
        font-weight: 600;
        color: #1a1a1a;
        text-align: center;
      }
    }

    .description {
      margin-bottom: 24px;
      font-size: 16px;
      line-height: 1.8;
      color: #333;

      p {
        margin-bottom: 1rem;
        text-indent: 2em;
      }
    }

    .businessScope {
      margin-left: 1rem;

      .scopeItem {
        margin-bottom: 1rem;
        font-size: 16px;
        line-height: 1.8;
        color: #333;
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;

        span {
          flex-shrink: 0;
          width: 2em;
        }
      }
    }

    // v-html渲染的内容样式
    :global(.business-scope) {
      margin-left: 1rem;

      .scope-item {
        margin-bottom: 1rem;
        font-size: 16px;
        line-height: 1.8;
        color: #333;
        display: flex;
        align-items: flex-start;
        gap: 0.5rem;

        span {
          flex-shrink: 0;
          width: 2em;
        }
      }
    }
  }
}
</style>

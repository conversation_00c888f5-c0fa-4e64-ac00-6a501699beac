<template>
  <div :class="$style.developmentHistory">
    <h2 :class="$style.title">发展历程</h2>
    <div v-if="Object.keys(props.historyData).length">
      <div :class="$style.timeline">
        <div v-for="(item, index) in displayData" :key="item.id || index" :class="$style.timelineItem">
          <div :class="$style.timePoint">
            <div :class="$style.timeCircleW">
              <div :class="$style.timeCircleN">{{ index + 1 }}</div>
            </div>
            <div :class="$style.timeLine" v-if="index < displayData.length - 1"></div>
          </div>

          <div :class="$style.timeContent">
            <div :class="$style.timeHeader">
              <span :class="$style.timeText">{{ item.title }}</span>
            </div>
            <div :class="$style.timeDescription" v-if="item.description">
              {{ item.description }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="text-center com-pc-portal-container" v-else>
      <ComEmpty />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { PortalIntroduceTitle, PortalIntroductionVo } from '../type';
import ComEmpty from '@/components/empty/index.vue';

defineOptions({ name: 'DevelopmentHistory' });

interface Props {
  historyData?: PortalIntroductionVo;
}

const props = withDefaults(defineProps<Props>(), {
  historyData: () => ({}),
});

// 处理发展历程数据
const displayData = computed(() => {
  const historyList = props.historyData?.portalIntroduceTitleList;
  if (historyList && historyList.length > 0) {
    return historyList.map((item: PortalIntroduceTitle, index) => ({
      id: item.px || String(index),
      title: item.bt || '',
      description: item.fbt || '',
      sort: item.px,
    }));
  }

  return [];
});
</script>

<style module lang="scss">
.developmentHistory {
  margin-top: 30px;
  .title {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 32px;
  }

  .timeline {
    position: relative;
    max-width: 800px;
  }

  .timelineItem {
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    position: relative;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .timePoint {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 32px;
    position: relative;
    margin-top: 24px;

    .timeCircleW {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: rgba(12, 135, 229, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }

    .timeCircleN {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      background: #0c87e5;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 12px;
    }

    .timeLine {
      position: absolute;
      top: 38px;
      width: 2px;
      height: 107px;
      background: rgba(200, 214, 225, 0.5);
    }
  }

  .timeContent {
    flex: 1;
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: relative;

    .timeHeader {
      margin-bottom: 12px;

      .timeText {
        font-size: 18px;
        font-weight: 600;
        color: #0c87e5;
      }
    }

    .timeDescription {
      font-size: 16px;
      line-height: 1.6;
      color: #666;
    }
  }
}
</style>

/**
 * ResultModelPortalIntroductionVo
 */
export interface Response {
  /**
   * 状态码
   * 状态码,200正常
   */
  code?: number;
  /**
   * 数据
   * 返回数据
   */
  data?: PortalIntroductionVo;
  /**
   * 消息
   * 返回提示
   */
  message?: string;
  [property: string]: any;
}

/**
 * 数据
 * 返回数据
 *
 * PortalIntroductionVo
 */
export interface PortalIntroductionVo {
  /**
   * 标题
   */
  bt?: string;
  /**
   * 发布时间
   */
  fbsj?: string;
  /**
   * 发布者
   */
  fbz?: string;
  /**
   * 发布状态值
   */
  fbzt?: string;
  /**
   * 发布状态
   */
  fbztName?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 标题列表
   */
  portalIntroduceTitleList?: PortalIntroduceTitle[];
  /**
   * 审核人
   */
  shrName?: string;
  /**
   * 宣传图
   */
  xctFileAttachmentList?: FileAttachment[];
  /**
   * 正文
   */
  zw?: string;
  [property: string]: any;
}

/**
 * 发展历程
 *
 * PortalIntroduceTitle
 */
export interface PortalIntroduceTitle {
  /**
   * 标题
   */
  bt?: string;
  /**
   * 副标题
   */
  fbt?: string;
  /**
   * 排序
   */
  px?: number;
  [property: string]: any;
}

/**
 * 附件表
 *
 * FileAttachment
 */
export interface FileAttachment {
  /**
   * 业务数据
   */
  businessData?: string;
  /**
   * 业务ID
   */
  businessId?: string;
  /**
   * 业务类型
   */
  businessType?: string;
  /**
   * 文件名称
   */
  fileName?: string;
  /**
   * 文件存放路径
   */
  filePath?: string;
  /**
   * 文件大小
   */
  fileSize?: string;
  /**
   * ID
   * 主键
   */
  id?: string;
  /**
   * 是否删除：0-未删除 1-已删除
   */
  isDel?: string;
  /**
   * 文件后缀
   */
  suffix?: string;
  /**
   * 上传日期
   */
  uploadDate?: string;
  /**
   * 视频时长
   */
  videoTime?: number;
  [property: string]: any;
}

/**
 * ResultModelPageModelPortalSocietyConstitutionPageVo
 */
export interface Response {
  /**
   * 状态码
   * 状态码,200正常
   */
  code?: number;
  /**
   * 数据
   * 返回数据
   */
  data?: PageModelPortalSocietyConstitutionPageVo;
  /**
   * 消息
   * 返回提示
   */
  message?: string;
  [property: string]: any;
}

/**
 * 数据
 * 返回数据
 *
 * PageModelPortalSocietyConstitutionPageVo
 */
export interface PageModelPortalSocietyConstitutionPageVo {
  pageNo?: number;
  pages?: number;
  pageSize?: number;
  rows?: PortalSocietyConstitutionPageVo[];
  total?: number;
  [property: string]: any;
}

/**
 * 学会章程
 *
 * PortalSocietyConstitutionPageVo
 */
export interface PortalSocietyConstitutionPageVo {
  /**
   * 标题
   */
  bt?: string;
  /**
   * 发布时间
   */
  fbsj?: string;
  /**
   * 发布者
   */
  fbz?: string;
  /**
   * 发布状态值
   */
  fbzt?: string;
  /**
   * 发布状态
   */
  fbztName?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 排序
   */
  px?: number;
  /**
   * 审核人
   */
  shrName?: string;
  /**
   * 正文
   */
  zw?: string;
  [property: string]: any;
}

/**
 * ResultModelListPortalOrgOfficePageVo
 */
export interface Response {
  /**
   * 状态码
   * 状态码,200正常
   */
  code?: number;
  /**
   * 数据
   * 返回数据
   */
  data?: PortalOrgOfficePageVo[];
  /**
   * 消息
   * 返回提示
   */
  message?: string;
  [property: string]: any;
}

/**
 * 学会办事机构查询
 *
 * PortalOrgOfficePageVo
 */
export interface PortalOrgOfficePageVo {
  /**
   * 发布时间
   */
  fbsj?: string;
  /**
   * 发布者
   */
  fbz?: string;
  /**
   * 发布状态值
   */
  fbzt?: string;
  /**
   * 发布状态
   */
  fbztName?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 机构简介
   */
  jgjj?: string;
  /**
   * 机构名称
   */
  jgmc?: string;
  /**
   * 排序
   */
  px?: number;
  /**
   * 审核人
   */
  shrName?: string;
  [property: string]: any;
}

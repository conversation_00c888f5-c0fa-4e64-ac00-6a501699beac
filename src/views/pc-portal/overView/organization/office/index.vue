<template>
  <div :class="$style.officePage">
    <n-spin :show="loading">
      <div v-if="officeData && officeData.length > 0">
        <h2 :class="$style.title">办事机构</h2>

        <div :class="$style.leadersGrid">
          <div v-for="leader in officeData" :key="leader.id" :class="$style.leaderCard">
            <div :class="$style.name">{{ leader.jgmc }}</div>
            <div :class="$style.position">{{ leader.jgjj }}</div>
          </div>
        </div>
      </div>
      <div v-else>空</div>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { Response, PortalOrgOfficePageVo } from './type';
import { showOfficeListAPI } from '../fetchData';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/utils/useNaivePagination.ts';

const officeData = ref<PortalOrgOfficePageVo[]>([]);

const [loading, search] = useAutoLoading(true);
const { updateTotal } = useNaivePagination(getLeader);

function getLeader() {
  search(
    showOfficeListAPI().then((res: Response) => {
      officeData.value = res.data || [];
      updateTotal(0);
    })
  );
}

onMounted(() => {
  getLeader();
});
defineOptions({ name: 'OfficeIndex' });
</script>

<style module lang="scss">
.officePage {
  width: 100%;

  .title {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 2rem;
    text-align: left;
  }

  .leadersGrid {
    background: #fff;
    .leaderCard {
      height: 102px;
      padding-left: 25px;
      text-align: center;
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      justify-content: center;
      margin-bottom: 10px;

      .name {
        font-size: 18px;
        font-weight: 600;
        color: #1a1a1a;
        margin-bottom: 14px;
      }
      .position {
        font-family:
          Alibaba PuHuiTi 2,
          Alibaba PuHuiTi 20;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
      }
    }
  }
}
</style>

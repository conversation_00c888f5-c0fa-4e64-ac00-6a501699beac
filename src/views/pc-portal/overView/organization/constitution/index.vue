<template>
  <div :class="$style.branchOfficePage">
    <n-spin :show="loading">
      <div v-if="tableData && tableData.length > 0">
        <h2 :class="$style.title">学会分支机构</h2>
        <div :class="$style.tableContainer">
          <n-data-table :columns="columns" :data="tableData" :max-height="400" :scroll-x="750" striped size="medium" />
        </div>
      </div>
      <div class="text-center com-pc-portal-container" v-else>
        <ComEmpty />
      </div>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { NDataTable } from 'naive-ui';
import ComEmpty from '@/components/empty/index.vue';
import { branchOfficeColumns } from './columns';
import { showOrgBranchListAPI } from '../fetchData';
import type { Response, PortalOrgBranchPageVo } from './types';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/utils/useNaivePagination.ts';

const tableData = ref<PortalOrgBranchPageVo[]>([]);
const columns = branchOfficeColumns;
const [loading, search] = useAutoLoading(true);
const { updateTotal } = useNaivePagination(loadBranchOfficeData);

/**
 * 加载分支机构数据
 */
function loadBranchOfficeData() {
  search(
    showOrgBranchListAPI().then((res: Response) => {
      tableData.value = res.data || [];
      updateTotal(0);
    })
  );
}

onMounted(() => {
  loadBranchOfficeData();
});

defineOptions({ name: 'BranchOfficeIndex' });
</script>

<style module lang="scss">
.branchOfficePage {
  width: 100%;

  .title {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 32px;
    text-align: left;
  }

  .tableContainer {
    background-color: #fff;
    padding: 20px;
  }
}
</style>

/**
 * 分支机构表格列配置
 */
import type { DataTableColumns } from 'naive-ui';
import type { PortalOrgBranchPageVo } from './types';

export const branchOfficeColumns: DataTableColumns<PortalOrgBranchPageVo> = [
  {
    title: '组织管理部门',
    key: 'jgmc',
    width: 300,
    align: 'center',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '成立时间',
    key: 'clsj',
    width: 200,
    align: 'center',
  },
  {
    title: '责任人',
    key: 'zrr',
    width: 150,
    align: 'center',
  },
];

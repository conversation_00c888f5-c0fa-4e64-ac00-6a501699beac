import { api } from '@/api';
import { IObj } from '@/types';
import { $http } from '@tanzerfe/http';

// 展示学会领导
export function showLeaderListAPI() {
  const url = api.getUrl(api.type.portal, api.name.overview.showLeaderList);
  return $http.post<any>(url, { data: { _cfg: { showTip: true } } });
}

// 展示办事机构
export function showOfficeListAPI() {
  const url = api.getUrl(api.type.portal, api.name.overview.showOfficeList);
  return $http.post<any>(url, { data: { _cfg: { showTip: true } } });
}

// 展示学会分支
export function showOrgBranchListAPI() {
  const url = api.getUrl(api.type.portal, api.name.overview.showOrgBranchList);
  return $http.post<any>(url, { data: { _cfg: { showTip: true } } });
}

// 展示学会理事单位
export function showOrgUnitListAPI(params: IObj<any>) {
  const url = api.getUrl(api.type.portal, api.name.overview.showOrgUnitList);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}

<template>
  <div :class="$style.directorUnitPage">
    <h2 :class="$style.title">理事单位</h2>

    <div :class="$style.contentContainer">
      <!-- 左侧分类菜单 -->
      <CategorySidebar @category-change="handleCategoryChange" />

      <!-- 右侧单位列表 -->
      <UnitList ref="unitListRef" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue';
import CategorySidebar from './comp/CategorySidebar.vue';
import UnitList from './comp/UnitList.vue';

const activeCategory = ref<string>('');
const unitListRef = ref();

/**
 * 处理分类变化
 */
const handleCategoryChange = (category: string) => {
  console.log('category', category);
  activeCategory.value = category;
  nextTick(() => {
    unitListRef.value?.getUnitList(category);
  });
};

defineOptions({ name: 'DirectorUnitIndex' });
</script>

<style module lang="scss">
.directorUnitPage {
  width: 100%;

  .title {
    font-size: 28px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 32px;
    text-align: left;
  }

  .contentContainer {
    display: flex;
    gap: 24px;
    border-radius: 4px;
    overflow: hidden;
    height: 500px;
  }
}
</style>

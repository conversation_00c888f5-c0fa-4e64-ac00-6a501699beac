/**
 * ResultModelListPortalOrgUnitPageVo
 */
export interface Response {
  /**
   * 状态码
   * 状态码,200正常
   */
  code?: number;
  /**
   * 数据
   * 返回数据
   */
  data?: PortalOrgUnitPageVo[];
  /**
   * 消息
   * 返回提示
   */
  message?: string;
  [property: string]: any;
}

/**
 * 理事单位查询
 *
 * PortalOrgUnitPageVo
 */
export interface PortalOrgUnitPageVo {
  /**
   * 机构简介
   */
  dwlx?: string;
  /**
   * 机构类型名称
   */
  dwlxName?: string;
  /**
   * 机构名称
   */
  dwmc?: string;
  /**
   * 发布时间
   */
  fbsj?: string;
  /**
   * 发布者
   */
  fbz?: string;
  /**
   * 发布状态值
   */
  fbzt?: string;
  /**
   * 发布状态
   */
  fbztName?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 审核人
   */
  shrName?: string;
  [property: string]: any;
  name?: string;
  value?: string;
}

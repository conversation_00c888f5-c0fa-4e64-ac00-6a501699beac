<template>
  <div :class="$style.unitListContainer">
    <div :class="$style.unitHeader">
      <span :class="$style.headerTitle">单位名称</span>
    </div>

    <n-spin :show="loading">
      <div :class="$style.unitList" v-if="unitList && unitList.length > 0">
        <div v-for="unit in unitList" :key="unit.id" :class="$style.unitItem">
          {{ unit.dwmc }}
        </div>
      </div>
      <div v-else class="h-[200px]">
        <ComEmpty />
      </div>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ComEmpty from '@/components/empty/index.vue';
import { showOrgUnitListAPI } from '../../fetchData';
import { Response, PortalOrgUnitPageVo } from '../types';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/utils/useNaivePagination.ts';

const [loading, search] = useAutoLoading(true);
const { updateTotal } = useNaivePagination(getUnitList);
const unitList = ref<PortalOrgUnitPageVo[]>([]);

function getUnitList(key: string | number) {
  let params = {
    pageNo: 1,
    pageSize: -1,
    xslx: key,
  };
  search(
    showOrgUnitListAPI(params)
      .then((res: Response) => {
        unitList.value = res.data || [];
        updateTotal(0);
      })
      .finally(() => {
        loading.value = false;
      })
  );
}

defineExpose({
  getUnitList,
});

defineOptions({ name: 'UnitList' });
</script>

<style module lang="scss">
.unitListContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #fff;
  padding: 22px;

  .unitHeader {
    height: 60px;
    line-height: 60px;
    text-align: center;
    background: #edf1f5;

    .headerTitle {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  .unitList {
    flex: 1;
    padding: 0;
    max-height: 440px;
    overflow-y: auto;

    .unitItem {
      height: 60px;
      line-height: 60px;
      text-align: center;
      padding: 0 24px;
      font-size: 14px;
      color: #666;
      border-bottom: 1px solid #f0f0f0;
      transition: background 0.2s ease;

      &:hover {
        background: #f8f9fa;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .com-pc-portal-empty {
    // top: calc(50% - 172px) !important;
  }
}
</style>

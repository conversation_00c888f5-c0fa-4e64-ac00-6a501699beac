<template>
  <div :class="$style.categorySidebar">
    <n-spin :show="loading">
      <div v-if="categories && categories.length > 0">
        <div
          v-for="category in categories"
          :key="category.value"
          :class="[$style.categoryItem, { [$style.active]: activeCategory === category.value }]"
          @click="handleCategoryClick(category.value)"
        >
          {{ category.label }}
        </div>
      </div>
      <div class="text-center" v-else>
        <ComEmpty />
      </div>
    </n-spin>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import ComEmpty from '@/components/empty/index.vue';
import { queryDictDataByTypeAPI } from '@/views/pc-portal/learndynamic/fetchData';
import { Response, PortalOrgUnitPageVo } from '../types';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useNaivePagination } from '@/utils/useNaivePagination.ts';

const emit = defineEmits(['categoryChange']);
const categories = ref<PortalOrgUnitPageVo[]>([]);
const activeCategory = ref<string | undefined>('');
const [loading, search] = useAutoLoading(true);
const { updateTotal } = useNaivePagination(getUnit);

// 处理分类点击
function handleCategoryClick(categoryKey: string | undefined) {
  activeCategory.value = categoryKey;
  emit('categoryChange', categoryKey);
}

// 获取理事单位
function getUnit() {
  search(
    queryDictDataByTypeAPI({ type: 'dwlx' }).then((res: Response) => {
      categories.value =
        res.data?.map((item) => {
          return {
            label: item.dictLabel,
            value: item.dictValue,
          };
        }) || [];
      updateTotal(0);
      activeCategory.value = categories.value[0].value;
      emit('categoryChange', activeCategory.value);
    })
  );
}

onMounted(() => {
  getUnit();
});

defineOptions({ name: 'CategorySidebar' });
</script>

<style module lang="scss">
.categorySidebar {
  width: 318px;
  background: #f8f9fa;
  padding: 22px;

  .categoryItem {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 18px;
    color: #212121;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
    margin-bottom: 12px;

    &:hover {
      background: #e9ecef;
      color: #333;
    }

    &.active {
      background: #e0f2ff;
      color: #0c87e5;
      font-weight: 600;
      border: 1px solid #0c87e5;
    }
  }

  .com-pc-portal-empty {
    top: calc(50% - 172px) !important;
  }
}
</style>

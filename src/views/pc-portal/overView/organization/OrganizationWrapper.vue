<template>
  <ComBread :data="breadData" />
  <OrganizationIndex :active-sub-tab="props.activeSubTab" class="mt-[20px]" />
</template>

<script setup lang="ts">
import OrganizationIndex from './index.vue';
import type { IBreadData } from '@/types';
import ComBread from '@/components/breadcrumb/ComBread.vue';

interface Props {
  activeSubTab?: string;
}

const breadData: IBreadData[] = [
  { name: '学会概况', routeRaw: { path: 'overview' }, clickable: true },
  { name: '组织架构' },
];
const props = withDefaults(defineProps<Props>(), {
  activeSubTab: 'leadership',
});

defineOptions({ name: 'OrganizationWrapper' });
</script>

<style module lang="scss">
.organizationWrapper {
  width: 100%;
}
</style>

<template>
  <div class="w-full h-full overview">
    <!-- 页面头部导航 -->
    <ComPortalPageHeader
      page-title="公共安全科学技术学会"
      :tabs="tabs"
      :sub-tabs="currentSubTabs"
      :breadcrumbs="breadcrumbs"
      :default-tab="activeTab"
      :default-sub-tab="activeSubTab"
      @tab-change="handleTabChange"
      @sub-tab-change="handleSubTabChange"
    />

    <!-- 主体内容区域 -->
    <div class="com-pc-portal-container">
      <!-- 动态组件切换 -->
      <component :is="currentComponent" :active-sub-tab="activeSubTab" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import ComPortalPageHeader, { type ITab, type ISubTab } from '@/views/pc-portal/common/comp/ComPortalPageHeader.vue';
import IntroductionTab from './introduction/index.vue';
import OrganizationWrapper from './organization/OrganizationWrapper.vue';
import LearnCharter from './leadership/index.vue';
// import HistoryTab from './history/index.vue';

const route = useRoute();
const activeTab = ref('introduction');
const activeSubTab = ref('leadership');

// Tab配置
const tabs = ref<ITab[]>([
  { name: 'introduction', label: '学会简介' },
  { name: 'organization', label: '组织架构' },
  { name: 'LearnCharter', label: '学会章程' },
  // { name: 'history', label: '发展历程' },
]);

// 面包屑配置
const breadcrumbs = ref<{ name: string }[]>([
  { name: '学会概况' },
  { name: '学会简介' }, // 这个会根据activeTab动态更新
]);

// 组织架构的二级菜单配置
const organizationSubTabs = ref<ISubTab[]>([
  { name: 'leadership', label: '学会领导' },
  { name: 'office', label: '办事机构' },
  { name: 'constitution', label: '学会分支机构' },
  { name: 'management', label: '理事单位' },
]);

// 当前二级菜单（只有组织架构有二级菜单）
const currentSubTabs = computed(() => {
  return activeTab.value === 'organization' ? organizationSubTabs.value : [];
});

// 组件映射关系
const componentMap = {
  introduction: IntroductionTab,
  organization: OrganizationWrapper,
  LearnCharter: LearnCharter,
  // history: HistoryTab,
};

// 当前激活的组件
const currentComponent = computed(() => {
  return componentMap[activeTab.value as keyof typeof componentMap] || IntroductionTab;
});

function handleTabChange(value: string, tab: ITab) {
  activeTab.value = value;
  // 切换到组织架构时，重置二级菜单
  if (value === 'organization') {
    activeSubTab.value = 'leadership';
  }
  console.log('Tab changed to:', tab.label);
}

function handleSubTabChange(value: string, subTab: ISubTab) {
  activeSubTab.value = value;
  console.log('SubTab changed to:', subTab.label);
}

// 初始化页面状态，支持URL参数切换
function initPageState() {
  const tabParam = route.query.tab as string;
  const subTabParam = route.query.subTab as string;

  // 如果有tab参数，使用参数值；否则重置为默认值
  if (tabParam && tabs.value.some((tab) => tab.name === tabParam)) {
    activeTab.value = tabParam;
  } else {
    activeTab.value = 'introduction';
  }

  // 如果有subTab参数且当前tab是organization，使用参数
  if (subTabParam && activeTab.value === 'organization') {
    const validSubTab = organizationSubTabs.value.find((subTab) => subTab.name === subTabParam);
    if (validSubTab) {
      activeSubTab.value = subTabParam;
    } else {
      activeSubTab.value = 'leadership';
    }
  } else {
    activeSubTab.value = 'leadership';
  }
}

onMounted(() => {
  console.log('onMounted - 当前路由:', route.path, route.query);
  initPageState();
});

// 监听路由变化，支持动态切换
watch(
  () => route.query,
  (newQuery) => {
    console.log('路由query变化:', newQuery);
    initPageState();
  },
  { deep: true }
);

defineOptions({ name: 'OverviewIntroduction' });
</script>

<style module lang="scss">
.overview {
  display: grid;
  grid-template-rows: 128px 32px 1fr;
}
.contentWrapper {
  padding: 5px;
  background: #f2f8fc;
  border-radius: 8px;
}
</style>

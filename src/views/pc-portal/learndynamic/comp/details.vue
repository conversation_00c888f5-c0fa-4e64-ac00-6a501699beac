<template>
  <div>
    <div class="com-pc-portal-container">
      <ComBread :data="breadData"></ComBread>
      <n-h1 class="text-center">{{ details.bt }}</n-h1>
      <div class="com-pc-portal-container-time">发布时间：{{ details.fbsj }}</div>
      <div v-html="details.zw"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import ComBread from '@/components/breadcrumb/ComBread.vue';
import { useRoute } from 'vue-router';
import { showNewsDetailsAPI } from '../fetchData';

const route = useRoute();
const breadData = ref<any>([
  {
    name: '学会动态',
    clickable: true,
    routeRaw: {
      name: 'pcPortalLearnDynamicIndex',
    },
  },
  {
    name: '详情',
  },
]);
const details = ref<any>({});
function getDetails() {
  showNewsDetailsAPI({
    id: route.query.id,
  }).then((res) => {
    console.log(res);
    details.value = res.data;
  });
}
onMounted(() => {
  getDetails();
});

defineOptions({ name: 'LearnDynamicDetails' });
</script>

<style module lang="scss">
// .comtime {
//   font-size: 14px;
//   border-bottom: #ddd solid 1px;
//   padding-bottom: 30px;
//   margin-bottom: 20px;
//   text-align: center;
//   color: #999999ff;
// }
</style>

import { api } from '@/api';
import { IObj } from '@/types';
import { $http } from '@tanzerfe/http';

// 获取学会动态列表
export function showNewsAPI(query: IObj<any>) {
  const url = api.getUrl(api.type.portal, api.name.portal.showNews, query);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...query } });
}
// 获取字典数据
export function queryDictDataByTypeAPI(query: IObj<any>) {
  const url = api.getUrl(api.type.portal, api.name.portal.queryDictDataByType, query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}
// 获取学会动态详情
export function showNewsDetailsAPI(query: IObj<any>) {
  const url = api.getUrl(api.type.portal, api.name.portal.showNewsDetails, query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}

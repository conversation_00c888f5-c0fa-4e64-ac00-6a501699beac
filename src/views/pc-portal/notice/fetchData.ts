import { api } from '@/api';
import { IObj } from '@/types';
import { $http } from '@tanzerfe/http';

// 获取通知公告列表
export function showNoticesAPI(query: IObj<any>) {
  const url = api.getUrl(api.type.portal, api.name.portal.showNotices);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...query } });
}
// 获取通知公告详情
export function showNoticesDetailsAPI(query: IObj<any>) {
  const url = api.getUrl(api.type.portal, api.name.portal.showNoticesDetails, query);
  return $http.get<any>(url, { data: { _cfg: { showTip: true } } });
}

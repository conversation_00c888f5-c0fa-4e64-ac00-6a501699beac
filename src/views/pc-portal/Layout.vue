<template>
  <n-config-provider :theme-overrides="theme.light(primaryColor)">
    <div :class="$style.PcPortalLayout">
      <Header />
      <div :class="$style.mainContent">
        <router-view />
      </div>
      <Footer />
    </div>
  </n-config-provider>
</template>

<script setup lang="ts">
import Header from './header/index.vue';
import Footer from './footer/index.vue';
import { NConfigProvider } from 'naive-ui';
import { naiveThemeOverrides } from '@/theme';
import { useCssVar } from '@vueuse/core';

const theme = naiveThemeOverrides();
const primaryColor = useCssVar('--com-pc-portal-primary-color', document.documentElement);

defineOptions({ name: 'PcPortalLayout' });
</script>

<style module lang="scss">
.PcPortalLayout {
  width: 100%;
  min-width: var(--com-pc-portal-content-width);
  display: grid;
  grid-auto-rows: auto minmax(calc(100vh - 292px - 108px - 72px), auto) auto;
  background: #f2f8fc;

  .mainContent {
    position: relative;
    width: 100%;
  }
}
</style>

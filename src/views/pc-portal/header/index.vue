<template>
  <header :class="$style.PcPortalHeader">
    <div>
      <PcLogo />

      <div :class="$style.search">
        <n-input-group>
          <n-input
            v-model:value="keyword"
            status="warning"
            size="large"
            placeholder="请输入想要搜索的内容"
            maxlength="50"
            round
            clearable
          />
          <n-button type="warning" size="large" style="--n-padding: 0 15px" round @click="handleSearch">
            <template #icon><IconSearch /></template>
            搜索
          </n-button>
        </n-input-group>
      </div>
    </div>
    <div>
      <div :class="$style.menuGroup">
        <n-menu
          :value="activeKey"
          mode="horizontal"
          :options="menuOptions"
          :theme-overrides="themeOverrides"
          @update-value="handleMenuClick"
        />

        <n-button type="primary" @click="handleLogin">会员登录/注册</n-button>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import PcLogo from '@/views/pc/logo/index.vue';
import { BySearch as IconSearch } from '@kalimahapps/vue-icons';
import { MenuOption, MenuProps } from 'naive-ui';
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { $toast } from '@/common/shareContext';
import { MessageReactive } from 'naive-ui/es/message/src/MessageProvider';

const themeOverrides: NonNullable<MenuProps['themeOverrides']> = {
  itemHeight: '72px',
  itemTextColorHorizontal: '#212121',
  itemTextColorHoverHorizontal: '#222',
  itemTextColorActiveHorizontal: '#fff',
  itemTextColorActiveHoverHorizontal: '#fff',
};

/**
 * 菜单
 * link 支持 路由path 以及 http链接
 */
const menuOptions: MenuOption[] = [
  { key: 'sy', label: '首页', link: '/' },
  { key: 'xhgk', label: '学会概况', link: '/overview' },
  { key: 'dhgz', label: '党建工作', link: '' },
  { key: 'xhdt', label: '学会动态', link: '/news' },
  { key: 'hyhd', label: '会议活动', link: '' },
  { key: 'hycz', label: '会员中心', link: '' },
  { key: 'zzjg', label: '分支机构', link: '/org?tab=organization&subTab=constitution' },
  { key: 'tzgg', label: '通知公告', link: '/notice' },
  { key: 'lxwm', label: '联系我们', link: '/contact' },
];

const router = useRouter();
const activeKey = ref();
const keyword = ref(router.currentRoute.value.query.keyword || '');

function handleSearch() {
  router.push({ name: 'pcPortalSearch', query: { keyword: keyword.value, timestamp: +new Date() } });
}

function handleMenuClick(key: string, item: MenuOption) {
  const link = (item.link || '') as string;

  if (!link) {
    tip(<string>item.label);
    return;
  }

  activeKey.value = key;

  if (link.startsWith('http')) {
    window.open(link, link);
  } else {
    const url = new URL(link, window.location.origin);
    const query: Record<string, string> = {};
    // 提取query参数
    url.searchParams.forEach((value, key) => {
      query[key] = value;
    });

    router.push({
      path: url.pathname,
      query: Object.keys(query).length > 0 ? query : undefined,
    });
  }
}

function handleLogin() {
  tip('会员登录/注册');
}

/**
 * 建设中提示
 * @param content
 */
function tip(content: string) {
  $toast.warning(`${content} 内容建设中`, {
    duration: 2000,
  });
}

function init() {
  let curMenuOpt = menuOptions[0];

  for (const item of menuOptions.slice(1)) {
    const routePath = (router.currentRoute.value.path || router.currentRoute.value.meta.parentPath || '') as string;
    const link = (<string>item.link).split('?')[0];

    if (link && routePath.startsWith(link)) {
      curMenuOpt = item;
      break;
    }
  }

  // 当前激活的菜单
  activeKey.value = curMenuOpt.key;
}

watch(() => router.currentRoute.value, init, { immediate: true });

defineOptions({ name: 'PcPortalHeader' });
</script>

<style module lang="scss">
.PcPortalHeader {
  > div:nth-child(1) {
    position: relative;
    width: var(--com-pc-portal-content-width);
    height: 108px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    pointer-events: none;
    background: url('./assets/bg.svg') -260px bottom no-repeat;
    background-size: 1920px 108px;
  }

  > div:nth-child(2) {
    height: 72px;
    background: url('./assets/bg-menu.svg') center center no-repeat;
    background-size: cover;
  }

  .menuGroup {
    width: var(--com-pc-portal-content-width) !important;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  :global(.n-menu.n-menu--horizontal .n-menu-item-content) {
    min-width: 100px;
    text-align: center;
  }

  :global(.n-menu.n-menu--horizontal .n-menu-item-content.n-menu-item-content--selected) {
    background: var(--com-pc-portal-primary-color);
  }

  .search {
    pointer-events: auto;
    position: absolute;
    bottom: 24px;
    right: 0;
    width: 372px;
  }
}
</style>

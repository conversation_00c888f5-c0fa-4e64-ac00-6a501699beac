<template>
  <div :class="$style.ComPortalTitle">
    <div>
      <slot name="title">
        <div :class="$style.title">{{ title }}</div>
      </slot>
    </div>

    <div>
      <slot name="more" v-if="hasMore">
        <n-button type="primary" text size="large" @click="handleMore">
          {{ more }}
        </n-button>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

const props = defineProps({
  title: String,
  hasMore: {
    type: Boolean,
    default: false,
  },
  more: {
    type: String,
    default: '查看更多 >>',
  },
  moreRouteName: {
    type: String,
    required: false,
  },
});

const emits = defineEmits(['moreClick']);
const router = useRouter();

function handleMore() {
  if (props.moreRouteName) {
    router.push({ name: props.moreRouteName });
  } else {
    emits('moreClick');
  }
}

defineOptions({ name: 'ComPortalTitle' });
</script>

<style module lang="scss">
.ComPortalTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 42px;

  .title {
    font-size: 30px;
    font-weight: 700;
  }
}
</style>

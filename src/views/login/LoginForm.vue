<template>
  <div :class="$style['form-wrap']">
    <n-form
      ref="formRef"
      class="com-autofill-none"
      :model="formVal"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
      size="large"
    >
      <n-form-item label="" path="phoneNumber" ref="phoneRef">
        <n-input v-model:value="formVal.phoneNumber" placeholder="请输入手机号" clearable> </n-input>
      </n-form-item>
      <n-form-item label="" path="verifyCode" style="margin-top: 15px">
        <n-input v-model:value="formVal.verifyCode" placeholder="请输入验证码">
          <template #suffix>
            <div class="text-[#008982]" @click="sendCaptchaSms">{{ captchaLabel }}</div>
          </template>
        </n-input>
      </n-form-item>

      <div :class="$style['btn-login']">
        <n-button type="primary" :loading="loading" @click="handleValidateSubmit" size="large" class="!text-white">
          立即登录
        </n-button>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { FormInst, FormRules } from 'naive-ui';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useAuthStore } from '@/store/auth';
import { useCaptcha } from '@/views/login/useCaptcha.ts';
import { isMobilePhone } from '@/utils/validate.ts';

const formVal = ref({
  phoneNumber: null,
  verifyCode: null,
});
const rules: FormRules = {
  phoneNumber: {
    required: true,
    trigger: ['blur'],
    validator(rule, val) {
      if (!val) return new Error('请输入手机号码');
      return isMobilePhone(val) ? true : new Error('请输入正确的手机号码');
    },
  },
  verifyCode: {
    required: true,
    trigger: ['blur'],
    message: '请输入验证码',
  },
};
const authStore = useAuthStore();
const formRef = ref<FormInst | null>(null);
const [loading, submit] = useAutoLoading();
const { phoneRef, captchaTimer, captchaLabel } = useCaptcha();

function sendCaptchaSms() {
  captchaTimer.start(formVal.value.phoneNumber);
}

function handleValidateSubmit() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      if (loading.value) return;
      const params = { meetingId: '1', ...formVal.value };
      submit(authStore.login(params)).then(() => {});
    }
  });
}

defineOptions({ name: 'LoginFormNew' });
</script>

<style module lang="scss">
.form-wrap {
  padding: 0 16px;

  :global(.n-input) {
    border-radius: 10px;
  }

  :global(.n-input .n-input__input-el) {
    height: 56px;
  }

  .btn-login {
    margin-top: 16px;

    > button {
      width: 100%;
      height: 56px;
      border-radius: 10px;
    }
  }
}
</style>

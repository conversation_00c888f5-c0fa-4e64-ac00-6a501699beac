<template>
  <div :class="$style['page-login']" class="com-g-row-a1">
    <div :class="$style['logo']"></div>
    <LoginForm />
  </div>
</template>

<script lang="ts" setup>
import LoginForm from './LoginForm.vue';

defineOptions({ name: 'LoginIndex' });
</script>

<style module lang="scss">
.page-login {
  height: 100vh;
  padding-top: 70px;
  background: url('@/assets/login-bg.png') no-repeat center;
  background-size: cover;

  .logo {
    width: 100px;
    height: 100px;
    margin: 0 auto 36px;
    background: url('@/assets/log.png') no-repeat center;
    background-size: contain;
  }

  .name {
    font-size: 26px;
    text-align: center;
    padding: 24px 12px;
  }
}
</style>

import { computed, ref } from 'vue';
import { ValidateError } from '@/views/login/type';
import { postSendSmsForLogin } from '@/views/login/fetchData';

/**
 * 验证码相关逻辑
 */
export function useCaptcha() {
  // 手机号码表单
  const phoneRef = ref();

  // 验证码倒计时
  const captchaTimer = {
    timer: ref<any>(undefined),
    defaultCount: 60, // 验证码等待时间
    count: ref(0),
    start: (phoneNumber: string | null) => {
      if (captchaTimer.timer.value !== undefined) {
        return;
      }

      // 验证号码表单
      phoneRef.value.validate({
        callback(errors: ValidateError[] | undefined) {
          if (!errors) {
            captchaTimer.count.value = captchaTimer.defaultCount;
            captchaTimer.timer.value = setInterval(() => {
              if (captchaTimer.count.value > 0) {
                captchaTimer.count.value--;
              } else {
                captchaTimer.stop();
              }
            }, 1000);

            // 请求接口
            phoneNumber && postSendSmsForLogin({ phoneNumber });
          }
        },
      });
    },
    stop: () => {
      clearInterval(captchaTimer.timer.value);
      captchaTimer.timer.value = undefined;
      captchaTimer.count.value = 0;
    },
  };

  // 验证码按钮文案
  const captchaLabel = computed(() =>
    captchaTimer.timer.value !== undefined ? `${captchaTimer.count.value}s` : '发送验证码'
  );

  return {
    phoneRef,
    captchaTimer,
    captchaLabel,
  };
}

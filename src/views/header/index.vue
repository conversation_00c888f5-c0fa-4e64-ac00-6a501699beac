<template>
  <div :class="$style.headerWrap">
    <!--    <div v-if="backRouteName" :class="$style.icon" @click="handelBack">-->
    <!--      <IconBack />-->
    <!--    </div>-->
    <div :class="$style.title">{{ title }}</div>
  </div>
</template>

<script setup lang="ts">
import { IoSharpChevronBack as IconBack } from '@kalimahapps/vue-icons';
import { useRouter } from 'vue-router';
import { computed } from 'vue';

const router = useRouter();
const title = computed(() => router.currentRoute.value.meta.title || '');
// 返回路由名
const backRouteName = computed(() => {
  const n = router.currentRoute.value.meta.backRouteName;
  if (n === undefined) return 'back';
  return n;
});
// 返回路由参数
const backRouteQuery = computed(() => router.currentRoute.value.query);

function handelBack() {
  if (backRouteName.value === 'back') {
    router.back();
  } else {
    if (backRouteName.value && typeof backRouteName.value === 'string') {
      router.push({ name: backRouteName.value, query: backRouteQuery.value });
    }
  }
}

defineOptions({ name: 'HeaderIndex' });
</script>

<style module lang="scss">
.headerWrap {
  position: relative;
  background: var(--skin-c1);
  color: #fff;
  text-align: center;

  .icon {
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    font-size: 26px;
    width: 56px;
    padding-left: 16px;
    height: 100%;
    display: flex;
    justify-items: center;
    align-items: center;
  }

  .title {
    font-size: 16px;
    font-weight: bold;
  }
}
</style>

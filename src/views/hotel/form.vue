<template>
  <div :class="$style['hotel-wrap']" class="com-g-row-1a">
    <n-scrollbar content-class="h-full">
      <n-form
        :class="$style['hotel-form']"
        ref="formRef"
        :model="formData"
        :rules="rules"
        require-mark-placement="left"
        size="large"
      >
        <n-flex vertical :size="12">
          <div :class="$style['form-row']">
            <h3 :class="$style['form-row-title']">参会人信息</h3>
            <n-form-item path="userName" label="姓名">
              <n-input v-model:value="formData.userName" placeholder="请输入姓名" clearable />
            </n-form-item>
            <n-form-item path="phone" label="手机号码">
              <n-input v-model:value="formData.phone" placeholder="请输入手机号码" clearable />
            </n-form-item>
            <n-form-item path="unitName" label="单位名称">
              <n-input v-model:value="formData.unitName" placeholder="请输入单位名称" clearable />
            </n-form-item>
          </div>

          <div :class="$style['form-row']">
            <h3 :class="$style['form-row-title']">酒店信息</h3>
            <n-form-item path="meetingHotelId" label="入住酒店名称">
              <n-select
                v-model:value="formData.meetingHotelId"
                placeholder="请选择入住酒店"
                :options="hotelList"
                @update:value="changeHotel"
                label-field="hotel"
                value-field="id"
                clearable
              />
            </n-form-item>
            <n-form-item path="rzTime" label="入住时间">
              <n-date-picker
                class="w-full"
                v-model:formatted-value="formData.checkInTime"
                value-format="yyyy-MM-dd"
                type="date"
                placeholder="请选择入住时间"
                clearable
              />
            </n-form-item>
            <n-form-item path="ldTime" label="计划离店时间">
              <span :class="[$style['tips'], $style['tips-ld']]">（离店时间将用于安排接送车辆）</span>
              <n-date-picker
                class="w-full mt-[20px]"
                v-model:formatted-value="formData.checkOutTime"
                value-format="yyyy-MM-dd"
                type="date"
                placeholder="请选择计划离店时间"
                clearable
              />
            </n-form-item>
          </div>

          <div :class="$style['form-row']">
            <h3 :class="$style['form-row-title']">就餐信息</h3>
            <n-form-item path="jcsj" label="选择需要就餐时间">
              <span :class="[$style['tips'], $style['tips-jc']]">（离店时间将用于安排接送车辆）</span>
              <div :class="$style['time-box']">
                <div
                  :class="[$style['time-item'], selectTimeData.includes(item.id) ? $style['active'] : '']"
                  v-for="(item, index) of eatingTimeList"
                  :key="index"
                  @click="selectTime(item.id)"
                >
                  {{ item.mealTime }}
                </div>
              </div>
            </n-form-item>
          </div>

          <div :class="$style['form-row']">
            <h3 :class="$style['form-row-title']">活动信息</h3>
            <n-form-item
              class="mb-[10px]"
              :path="item[0].activityType === 'cg' ? 'cghd' : 'cjsl'"
              v-for="(item, key) of activityData"
              :label="'是否参加' + key"
              :key="key"
            >
              <div :class="[$style['time-box'], $style['activity-box']]">
                <div
                  :class="[$style['time-item'], sItem.selected ? $style['active'] : '']"
                  v-for="(sItem, inx) of item"
                  :key="inx"
                  @click="selectActivity(key, inx)"
                >
                  {{ sItem.activityChoose }}
                </div>
              </div>
            </n-form-item>
          </div>

          <div :class="$style['form-row']">
            <h3 :class="$style['form-row-title']">回程信息</h3>
            <n-form-item path="hcbc" label="回程高铁车次/飞机航班">
              <n-input v-model:value="formData.hcbc" placeholder="请输入回程所搭的高铁车次或飞机航班" clearable />
            </n-form-item>
            <n-form-item path="hcTime" label="回程时间">
              <n-input v-model:value="formData.hcsj" placeholder="请输入回程车牌或者机票出发时间" clearable />
            </n-form-item>
          </div>
        </n-flex>
      </n-form>
    </n-scrollbar>
    <div :class="$style['hotel-footer']">
      <n-button class="!w-full" type="primary" size="large" @click="validateHandle"> 提 交 </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { FormInst } from 'naive-ui';
import { useRouter } from 'vue-router';
import { getActivity, getMeetingHotel, getMeetingmealTime, submitUserHotel } from './fetchData.ts';
import { IHotel, IHotelTime } from './type.ts';
import { dayjs } from '@/utils/dayjs.ts';

const router = useRouter();
const formData = ref({
  userName: '',
  phone: '',
  unitName: '',
  meetingHotelName: '',
  meetingHotelId: null,
  checkInTime: null,
  checkOutTime: null,
  jcsj: '',
  hcbc: '',
  hcsj: null,
  cghd: '',
  cjsl: '',
  meetingId: '1',
});
const rules = {
  userName: {
    required: true,
    trigger: ['blur'],
    message: '请输入姓名',
  },
  phone: {
    required: true,
    trigger: ['blur'],
    message: '请输入手机号码',
  },
  checkInTime: {
    required: true,
    trigger: ['blur'],
    message: '请选择入住时间',
  },
  checkOutTime: {
    required: true,
    trigger: ['blur'],
    message: '请选择计划离店时间',
  },
  jcsj: {
    required: true,
    trigger: ['blur'],
    message: '请选择需要就餐时间',
  },
  cghd: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择是否参加',
  },
  cjsl: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择是否参加',
  },
};
const formRef = ref<FormInst | null>(null);
const hotelList = ref<IHotel[]>([]);
const eatingTimeList = ref<IHotelTime[]>([]);
const selectTimeData = ref<string[]>([]);
const activityData = ref<Record<string, any>>({});

function changeHotel(val: string, option: IHotel) {
  formData.value.meetingHotelName = option.hotel;
}

function selectTime(id: string) {
  if (selectTimeData.value.includes(id)) {
    selectTimeData.value = selectTimeData.value.filter((item) => item !== id);
  } else {
    selectTimeData.value.push(id);
  }
  formData.value.jcsj = selectTimeData.value.join(',');
}

function selectActivity(key: string, index: number) {
  activityData.value[key].forEach((item: any, inx: number) => {
    item.selected = index === inx ? !item.selected : false;
  });

  formData.value.cghd = '';
  formData.value.cjsl = '';

  for (const i in activityData.value) {
    activityData.value[i].forEach((item: any) => {
      if (item.selected) {
        if (item.activityType === 'cg') {
          formData.value.cghd = item.id + '';
        }
        if (item.activityType === 'sl') {
          formData.value.cjsl = item.id + '';
        }
      }
    });
  }
}

function validateHandle() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      const time = dayjs().format('YYYY-MM-DD HH:mm:ss');
      submitUserHotel(formData.value).then(() => {
        router.replace({ name: 'hotelSuccess', query: { time, name: formData.value.userName } });
      });
    }
  });
}

getMeetingHotel().then((res) => {
  hotelList.value = res.data || [];
});

getMeetingmealTime().then((res) => {
  eatingTimeList.value = res.data || [];
});

getActivity().then((res) => {
  activityData.value = res.data;
});

defineOptions({ name: 'HotelForm' });
</script>

<style module lang="scss">
.hotel-wrap {
  @apply w-full box-border bg-[#F4F4F4] p-0;

  .hotel-form {
    @apply w-full h-full;
  }

  .form-row {
    @apply p-[16px] bg-white;
  }

  .form-row-title {
    @apply text-[16px] leading-6 font-bold mb-[12px];
  }

  .tips {
    @apply absolute text-[12px] text-[#eb1919];
  }

  .tips-ld {
    @apply top-[-6px] left-0;
  }

  .tips-jc {
    @apply top-[-6px] left-0;
  }

  .time-box {
    @apply flex flex-wrap mt-[30px];

    .time-item {
      @apply h-[40px] mr-[20px] mb-[10px] px-[16px] leading-[40px] bg-[#f6f6f6];
      border-radius: 6px;

      &.active {
        @apply bg-[#fff] text-[#00948D];
        border: 1px solid #00948d;
      }
    }
  }

  .activity-box {
    @apply mt-[10px];
  }

  .hotel-footer {
    @apply w-full px-[16px] py-[20px];
  }
}
</style>

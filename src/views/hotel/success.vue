<template>
  <div :class="$style['hotel-success']">
    <div :class="$style['success-bg']"></div>

    <div :class="$style['bg-one']"></div>

    <div :class="$style['success-main']">
      <p :class="$style['success-label']">提交成功</p>
      <p :class="$style['success-people']">参会人员：{{ name }}</p>
      <p :class="$style['success-time']">提交时间：{{ time }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router';

const route = useRoute();
const name = route.query.name;
const time = route.query.time;

defineOptions({ name: 'hotelSuccess' });
</script>

<style module lang="scss">
.hotel-success {
  position: relative;
  height: 100%;
  background: #f4f4f4;

  .success-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 266px;
    background: linear-gradient(180deg, #008b84 1%, #f4f4f4 100%);
  }

  .bg-one {
    position: absolute;
    top: 10px;
    left: 50%;
    width: 350px;
    height: 175px;
    transform: translateX(-50%);
    background: url('./assets/success.png') no-repeat center;
    background-size: contain;
    z-index: 99;
  }

  .success-main {
    position: absolute;
    top: 120px;
    left: 50.2%;
    width: 310px;
    height: 252px;
    padding-top: 80px;
    transform: translateX(-50%);
    background: #ffffff;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.08);
    border-radius: 16px;

    p {
      text-align: center;
    }

    .success-label {
      margin-bottom: 20px;
      font-size: 22px;
      font-weight: 700;
    }

    .success-people {
      margin-bottom: 16px;
    }
  }
}
</style>

import { api } from '@/api';
import { $http } from '@tanzerfe/http/index.ts';
import { IHotel, IHotelTime } from './type.ts';

export function getMeetingHotel() {
  const url = api.getUrl(api.type.sus, api.name.sus.getMeetingHotel, { meetingId: '1' });
  return $http.post<IHotel[]>(url);
}

export function getMeetingmealTime() {
  const url = api.getUrl(api.type.sus, api.name.sus.getMeetingmealTime, { meetingId: '1' });
  return $http.post<IHotelTime[]>(url);
}

export function submitUserHotel(params: any) {
  const url = api.getUrl(api.type.sus, api.name.sus.submitUserHotel);
  return $http.post<any>(url, { data: { _cfg: { showTip: true }, ...params } });
}

export function getActivity() {
  const url = api.getUrl(api.type.sus, api.name.sus.getActivity, { meetingId: '1' });
  return $http.get<any>(url);
}

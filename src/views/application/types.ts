export interface IFormData {
  userName: string;
  phone: string;
  gender: string;
  studentStatus: string;
  position: string;
  positionRank: string;
  workUnit: string;
  mail: string;
  invoiceType: string;
  corporateName: string;
  acount: string;
  ticketDelivery: string;
  checkTakerMobile: string;
  checkTakerMail: string;
  meetingVenueId: string | null;
  meetingVenueName: string | null;
  registerAddress: string;
  registerMobile: string;
  bank: string;
  openAccount: string;
  [p: string]: string | null;
}

export interface IMeeting {
  id: string;
  meetingVenue: string;
  [p: string]: string;
}

export type IMeetingOpt = IMeeting[];

export interface IApplicationInfo {
  acount: string;
  bankAndAccount: string;
  checkTakerMail: string;
  checkTakerMobile: string;
  conferenceName: string;
  corporateName: string;
  createTime: string;
  createUserId: string;
  gender: string;
  id: string;
  invoiceName: string;
  invoiceType: string;
  mail: string;
  meetingId: string;
  meetingVenueId: string;
  meetingVenueName: string;
  orderNo: string;
  phone: string;
  position: string;
  positionRank: string;
  qrcodeAddress: string;
  registeredAddressMobile: string;
  signCode: string;
  signStatus: string;
  signTime: string;
  signUpPayTime: string;
  status: string;
  studentStatus: string;
  ticketDelivery: string;
  userName: string;
  userType: string;
  workUnit: string;
  registerAddress: string;
  registerMobile: string;
  bank: string;
  openAccount: string;
  [p: string]: string;
}

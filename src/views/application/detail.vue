<template>
  <div :class="$style['application-wrap']" class="com-g-row-1a">
    <div :class="$style['header-back']" @click="handelBack">
      <IconBack />
    </div>
    <n-scrollbar content-class="h-full">
      <n-flex vertical :size="12">
        <div :class="$style['detail-block']">
          <h3 :class="$style['detail-block-title']">报名信息</h3>
          <n-flex :class="$style['info-item']" justify="space-between">
            <span :class="$style['label']">姓名</span>
            <span :class="$style['value']">{{ info.userName }}</span>
          </n-flex>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">手机号</span>
            <span :class="$style['value']">{{ info.phone }}</span>
          </n-flex>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">性别</span>
            <span :class="$style['value']">{{ info.gender === '0' ? '男' : '女' }}</span>
          </n-flex>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">是否是学生</span>
            <span :class="$style['value']">{{ info.studentStatus === '0' ? '否' : '是' }}</span>
          </n-flex>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">职务</span>
            <span :class="$style['value']">{{ info.position }}</span>
          </n-flex>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">职称</span>
            <span :class="$style['value']">{{ info.positionRank }}</span>
          </n-flex>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">工作单位</span>
            <span :class="$style['value']">{{ info.workUnit }}</span>
          </n-flex>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">电子邮箱</span>
            <span :class="$style['value']">{{ info.mail }}</span>
          </n-flex>
        </div>
        <div :class="$style['detail-block']">
          <h3 :class="$style['detail-block-title']">发票信息</h3>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">发票类型</span>
            <span :class="$style['value']">{{ info.invoiceName }}</span>
          </n-flex>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">发票抬头</span>
            <span :class="$style['value']">{{ info.corporateName }}</span>
          </n-flex>
          <template v-if="info.invoiceType === '1'">
            <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
              <span :class="$style['label']">注册地址</span>
              <span :class="$style['value']">{{ info.registerAddress }}</span>
            </n-flex>
            <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
              <span :class="$style['label']">注册电话</span>
              <span :class="$style['value']">{{ info.registerMobile }}</span>
            </n-flex>
            <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
              <span :class="$style['label']">开户银行</span>
              <span :class="$style['value']">{{ info.bank }}</span>
            </n-flex>
            <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
              <span :class="$style['label']">开户账户</span>
              <span :class="$style['value']">{{ info.openAccount }}</span>
            </n-flex>
          </template>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">纳税人识别号</span>
            <span :class="$style['value']">{{ info.acount }}</span>
          </n-flex>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">取票方式</span>
            <span :class="$style['value']">{{ info.ticketDelivery === '0' ? '电子票据' : '' }}</span>
          </n-flex>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">收票人手机</span>
            <span :class="$style['value']">{{ info.checkTakerMobile }}</span>
          </n-flex>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">收票人邮箱</span>
            <span :class="$style['value']">{{ info.checkTakerMail }}</span>
          </n-flex>
        </div>
        <div :class="$style['detail-block']">
          <h3 :class="$style['detail-block-title']">参会信息</h3>
          <n-flex :class="$style['info-item']" justify="space-between" :wrap="false">
            <span :class="$style['label']">分会场</span>
            <span :class="$style['value']">{{ info.meetingVenueName }}</span>
          </n-flex>
        </div>
      </n-flex>
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getApplicationDetail } from './fetch';
import { IApplicationInfo } from './types';
import { useRoute, useRouter } from 'vue-router';
import { IoSharpChevronBack as IconBack } from '@kalimahapps/vue-icons';

defineOptions({ name: 'ApplicationDetail' });

const router = useRouter();
const { query } = useRoute();

function handelBack() {
  router.push({
    name: query.backName as string,
    query: {
      id: query.id,
      t: +new Date(),
    },
  });
}

const info = ref<Partial<IApplicationInfo>>({
  userName: '',
  phone: '',
  gender: '',
  studentStatus: '',
  position: '',
  positionRank: '',
  workUnit: '',
  mail: '',
  invoiceType: '',
  corporateName: '',
  acount: '',
  ticketDelivery: '',
  checkTakerMobile: '',
  checkTakerMail: '',
  meetingVenueName: '',
  invoiceName: '',
  registerAddress: '',
  registerMobile: '',
  bank: '',
  openAccount: '',
});
function getDetail() {
  if (!query.id) return;
  getApplicationDetail({
    meetingUserId: query.id,
  }).then((res) => {
    info.value = res.data;
  });
}
getDetail();
</script>

<style module lang="scss">
.application-wrap {
  @apply w-full box-border bg-[#F4F4F4] p-0;

  .header-back {
    position: fixed;
    top: var(--header-height);
    left: 0;
    transform: translateY(-100%);
    font-size: 26px;
    color: #fff;
    width: 56px;
    height: var(--header-height);
    padding-left: 16px;
    display: flex;
    justify-items: center;
    align-items: center;
  }

  .detail-block {
    @apply p-[16px] bg-white;
  }
  .detail-block-title {
    @apply text-[16px] leading-6 font-bold mb-[12px];
  }

  .info-item {
    @apply text-[14px] py-[10px] leading-6;
    border-bottom: 1px solid #ebebeb;
    .label {
      white-space: nowrap;
    }
    .value {
      margin-left: 10px;
    }
  }
}
</style>

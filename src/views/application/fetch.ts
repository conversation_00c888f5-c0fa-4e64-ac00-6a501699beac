import { $http } from '@tanzerfe/http';
import { api } from '@/api/index';

import { IMeetingOpt, IApplicationInfo } from './types';

/*
 * 获取分会场list
 */
export function getMeetingList(params: any) {
  const url = api.getUrl(api.type.sus, api.name.sus.meetingVenue, params);
  return $http.post<IMeetingOpt>(url, { data: { _cfg: { showTip: true } } });
}

/*
 * 提交报名表单
 */
export function submitForm(params: any) {
  const url = api.getUrl(api.type.sus, api.name.sus.applicationForm);
  return $http.post<IApplicationInfo>(url, { data: { _cfg: { showTip: true }, ...params } });
}

/*
 * 报名信息
 */
export function getApplicationDetail(params: any) {
  const url = api.getUrl(api.type.sus, api.name.sus.applicationDetail, params);
  return $http.get<IApplicationInfo>(url, { data: { _cfg: { showTip: true } } });
}

import { ref } from 'vue';
import type { FormRules, FormItemRule, SelectOption } from 'naive-ui';
import { IFormData } from './types';
import { isMobilePhone, isTaxpayerNo, isChinese } from '@/utils/validate';
import { useAuthStore } from '@/store/auth';

const userStore = useAuthStore();
const _phone = userStore.userInfo.meetingUserInfo?.phone || '';

export const formData = ref<IFormData>({
  userName: '',
  phone: _phone,
  gender: '',
  studentStatus: '',
  position: '',
  positionRank: '',
  workUnit: '',
  mail: '',
  invoiceType: '',
  corporateName: '',
  acount: '',
  ticketDelivery: '0',
  checkTakerMobile: '',
  checkTakerMail: '',
  meetingVenueId: null,
  meetingVenueName: '',
  registerAddress: '',
  registerMobile: '',
  bank: '',
  openAccount: '',
});

export const rules: FormRules = {
  userName: {
    required: true,
    validator(rule: FormItemRule, value: string) {
      if (!value) {
        return new Error('请输入姓名');
      } else if (!isChinese(value)) {
        return new Error('请输入中文');
      }
      return true;
    },
    trigger: ['blur', 'change'],
  },
  phone: {
    required: true,
    validator(rule: FormItemRule, value: string) {
      if (!value) {
        return new Error('请输入手机号');
      } else if (!isMobilePhone(value)) {
        return new Error('请输入正确的手机号');
      }
      return true;
    },
    trigger: ['blur'],
  },
  gender: {
    required: true,
    message: '请选择性别',
    trigger: ['blur'],
  },
  studentStatus: {
    required: true,
    message: '请选择是否是学生',
    trigger: ['blur'],
  },
  position: {
    required: true,
    message: '请输入职务',
    trigger: ['blur'],
  },
  positionRank: {
    required: true,
    message: '请输入职称',
    trigger: ['blur'],
  },
  workUnit: {
    required: true,
    message: '请输入工作单位',
    trigger: ['blur'],
  },
  mail: {
    required: true,
    message: '请输入邮箱号',
    trigger: ['blur'],
  },
  invoiceType: {
    required: true,
    message: '请选择发票类型',
    trigger: ['blur'],
  },
  corporateName: {
    required: true,
    message: '请输入发票抬头',
    trigger: ['blur'],
  },
  acount: {
    required: true,
    message: '请输入纳税人识别号',
    validator(rule: FormItemRule, value: string) {
      if (!value) {
        return new Error('请输入纳税人识别号');
      } else if (!isTaxpayerNo(value)) {
        return new Error('请输入正确的纳税人识别号');
      }
      return true;
    },
    trigger: ['blur'],
  },
  ticketDelivery: {
    required: true,
    message: '请输入收票方式',
    trigger: ['blur'],
  },
  checkTakerMobile: {
    required: true,
    validator(rule: FormItemRule, value: string) {
      if (!value) {
        return new Error('请输入收票人手机号');
      } else if (!isMobilePhone(value)) {
        return new Error('请输入正确的手机号');
      }
      return true;
    },
    trigger: ['blur'],
  },
  checkTakerMail: {
    required: true,
    message: '请输入收票人邮箱',
    trigger: ['blur'],
  },
  meetingVenueId: {
    required: true,
    message: '请选择分会场',
    trigger: ['blur', 'change'],
  },
  registerAddress: {
    required: true,
    message: '请选择注册地址',
    trigger: ['blur'],
  },
  registerMobile: {
    required: true,
    message: '请输入注册电话',
    trigger: ['blur'],
  },
  bank: {
    required: true,
    message: '请选择开户银行',
    trigger: ['blur'],
  },
  openAccount: {
    required: true,
    message: '请选择开户账户',
    trigger: ['blur'],
  },
};

export const meetingPlaceOpt = ref<SelectOption[]>([]);

<template>
  <div :class="$style['application-wrap']" class="com-g-row-1a">
    <n-scrollbar content-class="h-full">
      <n-form
        :class="$style['application-form']"
        ref="formRef"
        :model="formData"
        :rules="rules"
        require-mark-placement="left"
        size="large"
      >
        <n-flex vertical :size="12">
          <div :class="$style['form-row']">
            <h3 :class="$style['form-row-title']">报名信息</h3>
            <n-form-item path="userName" label="姓名">
              <n-input v-model:value="formData.userName" placeholder="请输入姓名" />
            </n-form-item>
            <n-form-item path="phone" label="手机号">
              <n-input v-model:value="formData.phone" placeholder="请输入手机号" />
            </n-form-item>
            <n-form-item path="gender" label="性别">
              <n-radio-group v-model:value="formData.gender" name="sexRadio">
                <n-space>
                  <n-radio value="0"> 男 </n-radio>
                  <n-radio value="1"> 女 </n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
            <n-form-item path="studentStatus" label="是否是学生">
              <n-radio-group v-model:value="formData.studentStatus" name="studentRadio">
                <n-space>
                  <n-radio value="1"> 是 </n-radio>
                  <n-radio value="0"> 否 </n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
            <n-form-item path="position" label="职务">
              <n-input v-model:value="formData.position" placeholder="请输入职务" />
            </n-form-item>
            <n-form-item path="positionRank" label="职称">
              <n-input v-model:value="formData.positionRank" placeholder="请输入职称" />
            </n-form-item>
            <n-form-item path="workUnit" label="工作单位">
              <n-input v-model:value="formData.workUnit" placeholder="请输入工作单位" />
            </n-form-item>
            <n-form-item path="mail" label="电子邮箱">
              <n-input v-model:value="formData.mail" placeholder="请输入电子邮箱" />
            </n-form-item>
          </div>
          <div :class="$style['form-row']">
            <h3 :class="$style['form-row-title']">发票信息</h3>
            <n-form-item path="invoiceType" label="发票类型">
              <n-radio-group v-model:value="formData.invoiceType" name="invoiceRadio" @update:value="invoiceTypeChange">
                <n-space>
                  <n-radio value="0"> 增值税普通发票 </n-radio>
                  <n-radio value="1"> 增值税专用发票 </n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
            <n-form-item path="corporateName" label="发票抬头">
              <n-input v-model:value="formData.corporateName" placeholder="请输入发票抬头" />
            </n-form-item>
            <n-form-item path="acount" label="纳税人识别号">
              <n-input v-model:value="formData.acount" placeholder="请输入纳税人识别号" :maxlength="18" show-count />
            </n-form-item>
            <div v-if="formData.invoiceType === '1'">
              <n-form-item path="registerAddress" label="注册地址">
                <n-input v-model:value="formData.registerAddress" placeholder="请输入注册地址" />
              </n-form-item>
              <n-form-item path="registerMobile" label="注册电话">
                <n-input v-model:value="formData.registerMobile" placeholder="请输入注册电话" />
              </n-form-item>
              <n-form-item path="bank" label="开户银行">
                <n-input v-model:value="formData.bank" placeholder="请输入开户银行" />
              </n-form-item>
              <n-form-item path="openAccount" label="开户账户">
                <n-input v-model:value="formData.openAccount" placeholder="请输入开户账户" />
              </n-form-item>
            </div>
            <n-form-item path="ticketDelivery" label="取票方式">
              <n-radio-group v-model:value="formData.ticketDelivery" name="ticketDeliveryRadio">
                <n-space>
                  <n-radio value="0">电子票据 </n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
            <n-form-item path="checkTakerMobile" label="收票人手机">
              <n-input v-model:value="formData.checkTakerMobile" placeholder="请输入收票人手机" />
            </n-form-item>
            <n-form-item path="checkTakerMail" label="收票人邮箱">
              <n-input v-model:value="formData.checkTakerMail" placeholder="请输入收票人邮箱" />
            </n-form-item>
          </div>
          <div :class="$style['form-row']">
            <h3 :class="$style['form-row-title']">分会场信息</h3>
            <n-form-item path="meetingVenueId" label="选择分会场">
              <n-select
                v-model:value="formData.meetingVenueId"
                placeholder="请选择分会场"
                placement="top-start"
                :options="meetingPlaceOpt"
                label-field="meetingVenue"
                value-field="id"
                @update:value="selsectUpdated"
                clearable
              />
            </n-form-item>
          </div>
        </n-flex>
      </n-form>
    </n-scrollbar>
    <div :class="$style['application-footer']">
      <n-button class="!w-full" type="primary" size="large" @click="validateHandle"> 提 交 </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, toRaw } from 'vue';
import type { FormInst, FormItemRule } from 'naive-ui';
import { formData, rules, meetingPlaceOpt } from './formRefs';
import { submitForm, getMeetingList, getApplicationDetail } from './fetch';
import { IApplicationInfo } from './types';
import { useRouter, useRoute } from 'vue-router';

defineOptions({ name: 'ApplicationForm' });

const router = useRouter();
const { query } = useRoute();

async function getOpts() {
  try {
    const { data } = await getMeetingList({ meetingId: '1' });
    meetingPlaceOpt.value = data;
  } catch (error) {}
}
getOpts();

const selsectUpdated = (value: string, option: any) => {
  formData.value.meetingVenueName = option.meetingVenue;
};

const formRef = ref<FormInst | null>(null);

const invoiceTypeChange = (v: string) => {
  formData.value.registerAddress = '';
  formData.value.registerMobile = '';
  formData.value.bank = '';
  formData.value.openAccount = '';
};

const validateHandle = () => {
  let validateRule = undefined;
  if (formData.value.invoiceType === '0') {
    validateRule = (rule: FormItemRule) => {
      return !['registerAddress', 'registerMobile', 'bank', 'openAccount'].includes(rule?.key as string);
    };
  }
  formRef.value?.validate((errors) => {
    if (!errors) {
      submitFormHandle();
    }
  }, validateRule);
};
const submitFormHandle = () => {
  const params = toRaw(formData.value);
  submitForm({
    meetingId: '1',
    ...params,
  })
    .then((res) => {
      const _data = res.data;
      const routerName = +_data.status > 1 ? 'order' : 'payPay';
      router.push({
        name: routerName,
        query: {
          id: _data.id,
        },
      });
    })
    .catch(() => {});
};

// 获取最新信息, 修改表单
async function initFormData() {
  if (!query.id) return;

  try {
    const { data } = await getApplicationDetail({
      meetingUserId: query.id,
    });
    for (const key in formData.value) {
      if (Object.prototype.hasOwnProperty.call(formData.value, key)) {
        formData.value[key] = data[key] || '';
      }
    }
  } catch (error) {}
}
initFormData();
</script>

<style module lang="scss">
.application-wrap {
  @apply w-full box-border bg-[#F4F4F4] p-0;

  .application-form {
    @apply w-full h-full;
  }
  .form-row {
    @apply p-[16px] bg-white;
  }
  .form-row-title {
    @apply text-[16px] leading-6 font-bold mb-[12px];
  }

  .application-footer {
    @apply w-full px-[16px] py-[20px];
  }
}
</style>

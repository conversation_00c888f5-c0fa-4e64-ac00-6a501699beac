<template>
  <div :class="$style['registered-wrap']">
    <n-scrollbar v-if="registeredData.length">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <div
            class="mb-[10px]"
            v-for="(item, index) in registeredData"
            :key="index"
            :class="$style['registered-item']"
          >
            <div :class="$style['item-title']">
              <span>{{ item.userName }}的报名信息</span>
              <span @click="toDetail(item.meetingUserId)">查看{{ typeLabel }}信息</span>
            </div>

            <div :class="$style['info-item']">姓名：{{ item.userName }}</div>
            <div :class="$style['info-item']">手机号：{{ item.phone }}</div>
            <div :class="$style['info-item']">单位名称：{{ item.workUnit }}</div>
            <div :class="$style['info-item']">报名时间：{{ item.signUpPayTime }}</div>
          </div>
        </van-list>
      </van-pull-refresh>
    </n-scrollbar>

    <Empty v-else />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { querySignUserInfoList } from '../fetchData.ts';
import { IWork } from '../type.ts';
import Empty from '@/components/empty/index.vue';

const router = useRouter();
const props = defineProps({
  tabType: {
    type: String,
    default: '',
  },
  phone: {
    type: String,
    default: '',
  },
});
const typeLabel = computed(() => {
  if (props.tabType === '1') return '已签到';
  if (props.tabType === '2') return '未签到';
  return '报名';
});
const query = ref({ pageNo: 1, pageSize: 10 });
const total = ref(0);
const registeredData = ref<IWork[]>([]);
const refreshing = ref(false);
const loading = ref(false);
const finished = ref(false);

function toDetail(id: string) {
  router.push({
    name: 'applicationDetail',
    query: {
      id,
      backName: 'workbenches',
    },
  });
}

// 手机号及tab切换搜索查询
function search() {
  refreshing.value = true;
  onRefresh();
}

// 下拉刷新
function onRefresh() {
  finished.value = false;
  loading.value = true;
  onLoad();
}

// 上划加载
function onLoad() {
  if (refreshing.value) {
    query.value.pageNo = 1;
    registeredData.value = [];
    refreshing.value = false;
  }

  const params = { meetingId: '1', type: props.tabType, phone: props.phone, ...query.value };
  querySignUserInfoList(params).then((res) => {
    total.value = res.data.total || 0;
    loading.value = false;

    if (registeredData.value.length < total.value) {
      const data = res.data.rows || [];
      registeredData.value.push(...data);
      finished.value = false;
      ++query.value.pageNo;
    } else {
      finished.value = true;
    }
  });
}

watch(
  [() => props.tabType, () => props.phone],
  () => {
    search();
  },
  { immediate: true }
);

defineExpose({ search });
defineOptions({ name: 'RegisteredComp' });
</script>

<style module lang="scss">
.registered-wrap {
  height: 100%;

  .registered-item {
    margin-bottom: 12px;
    padding: 16px 18px;
    background-color: #fff;
    border-radius: 8px;

    .item-title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span:first-child {
        width: 0;
        flex: 1;
        color: #000;
        font-weight: 700;
        font-size: 17px;
      }
      span:last-child {
        color: #008982;
        font-size: 14px;
      }
    }

    .info-item {
      color: #333;
      margin-top: 10px;
    }
  }
}
</style>

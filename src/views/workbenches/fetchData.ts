import { api } from '@/api';
import { $http } from '@tanzerfe/http/index.ts';
import { IWork, IWx } from './type.ts';

export function querySignUserInfoList(params: any) {
  const url = api.getUrl(api.type.sus, api.name.sus.querySignUserInfoList, params);
  return $http.get<{ rows: IWork[]; total: number }>(url, { data: { _cfg: { showTip: true } } });
}

export function getSign(link: string) {
  const url = api.getUrl(api.type.sus, api.name.sus.getSign, { url: link });
  return $http.post<IWx>(url);
}

export function scanQrCode(signCode: string) {
  const url = api.getUrl(api.type.sus, api.name.sus.scanQrCode, { signCode });
  return $http.post(url, { data: { _cfg: { showOkTip: true, showTip: true } } });
}

//

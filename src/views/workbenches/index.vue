<template>
  <div :class="$style['workbenches-wrap']">
    <div :class="$style['search-box']">
      <n-input round placeholder="输入报名用户手机号进行搜索" v-model:value="phone" clearable>
        <template #prefix>
          <n-icon>
            <Search />
          </n-icon>
        </template>
      </n-input>
    </div>

    <div :class="$style['workbenches-content']">
      <n-tabs
        type="line"
        tab-class="workbenches-tab"
        pane-class="pane-content"
        v-model:value="tab"
        @update:value="changeTab"
      >
        <n-tab-pane name="0" tab="已报名"> </n-tab-pane>
        <n-tab-pane name="1" tab="已签到"> </n-tab-pane>
        <n-tab-pane name="2" tab="未签到"> </n-tab-pane>
      </n-tabs>
      <div :class="$style['main']">
        <RegisteredComp :tab-type="tab" :phone="phone" ref="registeredRef" />
      </div>
    </div>

    <div :class="$style['code-btn']">
      <n-button class="w-full" type="primary" @click="signIn">扫码签到</n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { AkSearch as Search } from '@kalimahapps/vue-icons';
import RegisteredComp from './registered/index.vue';
import { getSign, scanQrCode } from '@/views/workbenches/fetchData.ts';
import wx from 'weixin-jsapi';
import { PayService } from '@/views/pay/service/PayService.ts';
import { Env } from '@/views/pay/service/Env.ts';

const tab = ref('0');
const phone = ref('');
const registeredRef = ref<any>();

function changeTab() {
  phone.value = '';
}

function getWxConfig() {
  getSign(window.location.href.split('#')[0]).then((res) => {
    wx.config({
      debug: false,
      appId: PayService.Appid,
      timestamp: +res.data.timestamp,
      nonceStr: res.data.nonceStr,
      signature: res.data.signature,
      jsApiList: ['checkJsApi', 'scanQRCode'],
    });
  });
}

function signIn() {
  if (!Env.isWx) {
    alert('请在微信浏览器中打开');
    return;
  }

  wx.scanQRCode({
    needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
    scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
    success(res: any) {
      const arr = res.resultStr.split(',');
      const code = arr[arr.length - 1];
      // 转为数组时为了避免扫描一维码是返回CODE_128,20180528前面会添加一个CODE_128所以转为数组获		取最后一条就行了
      scanQrCode(code).finally(() => {
        registeredRef.value?.search();
      });
    },
  });
}

onMounted(() => {
  getWxConfig();
});
defineOptions({ name: 'WorkbenchesComp' });
</script>

<style module lang="scss">
.workbenches-wrap {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;

  .workbenches-content {
    flex: 1;
    height: 0;
    padding: 15px 0;
    display: flex;
    flex-direction: column;

    :global(.workbenches-tab) {
      font-size: 16px;
    }

    .main {
      flex: 1;
      height: 0;
    }
  }

  .code-btn {
    > button {
      width: 100%;
      height: 48px;
      border-radius: 8px;
      font-size: 20px;
    }
  }
}
</style>

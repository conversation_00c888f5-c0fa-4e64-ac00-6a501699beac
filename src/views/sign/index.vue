<template>
  <div :class="$style['sign-wrap']">
    <div :class="$style['sign-btn']">
      <n-button type="primary" size="large" class="!text-white" @click="signUp">立即报名</n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getApplicationDetail } from '@/views/application/fetch.ts';
import { useAuthStore } from '@/store/auth';
import { useRouter } from 'vue-router';

const router = useRouter();
const userStore = useAuthStore();

function signUp() {
  const id = userStore.userInfo.meetingUserInfo?.id;
  const status = userStore.userInfo.meetingUserInfo?.status;
  if (!id) return userStore.logout();

  // 0=未报名，1已报名未付款，2=已支付
  if (!status || status === '0') router.replace({ name: 'applicationForm' });
  if (status === '1') router.replace({ name: 'payPay', query: { id } });
  if (status === '2') router.replace({ name: 'order', query: { id } });
}

function getDetail() {
  const id = userStore.userInfo.meetingUserInfo?.id;
  if (!id) return;

  getApplicationDetail({
    meetingUserId: id,
  }).then((res) => {
    userStore.refresh(res.data);
  });
}

getDetail();

defineOptions({ name: 'signComp' });
</script>

<style module lang="scss">
.sign-wrap {
  position: relative;
  width: 100%;
  height: 393vw;
  color: #fff;
  background: url('@/assets/sign-bg.png') no-repeat center;
  background-size: cover;

  .sign-btn {
    position: absolute;
    left: 50%;
    bottom: 10vw;
    width: 90%;
    transform: translateX(-50%);

    > button {
      width: 100%;
      height: 56px;
      border-radius: 10px;
    }
  }
}
</style>

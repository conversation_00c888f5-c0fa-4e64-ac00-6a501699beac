export interface IDetailInfo {
  acount: string;
  bankAndAccount: string;
  checkTakerMail: string;
  checkTakerMobile: string;
  conferenceName: string;
  corporateName: string;
  createTime: string;
  createUserId: string;
  gender: string;
  id: string;
  invoiceName: string;
  invoiceType: string;
  mail: string;
  meetingId: string;
  meetingVenueId: string;
  meetingVenueName: string;
  orderNo: string;
  phone: string;
  position: string;
  positionRank: string;
  qrcodeAddress: string;
  registeredAddressMobile: string;
  signCode: string;
  signStatus: string;
  signTime: string;
  signUpPayTime: string;
  status: string;
  studentStatus: string;
  ticketDelivery: string;
  userName: string;
  userType: string;
  workUnit: string;
}

export type PickApplication = Pick<IDetailInfo, 'id' | 'userName' | 'phone' | 'workUnit' | 'createTime'>;

export type PickOrder = Pick<IDetailInfo, 'qrcodeAddress' | 'signCode' | 'status'>;

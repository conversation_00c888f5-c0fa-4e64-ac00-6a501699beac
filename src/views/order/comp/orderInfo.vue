<template>
  <div :class="$style['order-info']">
    <div :class="$style['order-header']">
      <div :class="$style['com-flex-row']">
        <success class="text-[24px]" />
        <span :class="$style['order-status']">报名成功</span>
      </div>
      <p :class="$style['order-status-tips']">恭喜您报名成功！</p>
    </div>
    <div :class="$style['order-main']">
      <div :class="[$style['card-header'], $style['com-flex-row']]">
        <img class="w-[60px] h-[60px]" :src="log" alt="" />
        <h3 :class="$style['unit-name']">2024年中国公共安全大会报名</h3>
        <div v-if="order.status === '3'" :class="$style['card-qrcode_used']"></div>
      </div>
      <div :class="$style['card-main']">
        <div :class="$style['card-qrcode-title']">请提醒主办方<span class="text-[#EC4B69]">【扫码核销】</span>使用</div>
        <div :class="$style['card-qrcode']">
          <img class="w-[194px] h-[194px]" :src="qrcodeUrl" alt="" />
        </div>
      </div>
      <div :class="$style['order-footer']">请妥善截图保存签到二维码用于会场签到</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import log from '@/assets/log.png';
import { AnOutlinedCheckCircle as success } from '@kalimahapps/vue-icons';
import noCode from '@/assets/no_qrcode.png';

defineOptions({ name: 'OrderInfo' });

const props = defineProps({
  order: {
    type: Object,
    default: () => ({
      qrcodeAddress: '',
      signCode: '',
      status: '',
    }),
  },
});

const qrcodeUrl = computed(() => {
  const _order = props.order;
  return _order.qrcodeAddress ? window.$SYS_CFG.apiBaseFile + _order.qrcodeAddress : noCode;
});
</script>

<style module lang="scss">
.order-info {
  @apply pt-[20px] text-[14px];

  .com-flex-row {
    @apply flex flex-row flex-nowrap items-center;
  }

  .order-header {
    @apply mb-[12px] text-white text-[22px];
  }
  .order-status {
    @apply ml-[7px];
  }
  .order-status-tips {
    @apply text-[14px] mt-[4px];
  }

  .order-main {
    @apply px-[10px] py-[18px] bg-white rounded-[8px];
  }

  .card-header {
    @apply relative pb-[16px] mb-[16px];
    border-bottom: 1px dashed #ebebeb;

    .card-qrcode_used {
      @apply absolute top-[-10px] right-[-10px] w-[75px] h-[75px];
      background: url('@/assets/order_used.png') 0 0 no-repeat;
      background-size: 100% 100%;
    }
  }
  .unit-name {
    @apply flex-1 text-[16px] leading-5 text-skin-t1 font-bold ml-[10px];
  }

  .card-main {
    @apply bg-[#EFFFFA] rounded-[8px] mb-[16px];
  }
  .card-qrcode-title {
    @apply relative leading-5 text-center py-[10px] bg-[#DBF6F4];
    &::before {
      @apply absolute bottom-0 left-1/2;
      content: '';
      transform: translate(-50%, 100%);
      width: 0;
      height: 0;
      border: 13px solid transparent;
      border-top: 13px solid #dbf6f4;
    }
  }
  .card-qrcode {
    @apply py-[37px] px-[65px] text-center;
  }

  .order-footer {
    @apply text-[#606266] text-center;
  }
}
</style>

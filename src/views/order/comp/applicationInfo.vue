<template>
  <div :class="$style['application-info']">
    <div :class="$style['info-main']">
      <h3 :class="$style['info-title']">报名信息</h3>

      <n-flex :class="$style['info-item']" :wrap="false">
        <span :class="$style['info-label']">姓名:</span>
        <span :class="$style['info-value']">{{ info.userName }}</span>
      </n-flex>
      <n-flex :class="$style['info-item']" :wrap="false">
        <span :class="$style['info-label']">手机号:</span>
        <span :class="$style['info-value']">{{ info.phone }}</span>
      </n-flex>
      <n-flex :class="$style['info-item']" :wrap="false">
        <span :class="$style['info-label']">单位名称:</span>
        <span :class="$style['info-value']">{{ info.workUnit }}</span>
      </n-flex>
      <n-flex :class="$style['info-item']" :wrap="false">
        <span :class="$style['info-label']">报名时间:</span>
        <span :class="$style['info-value']">{{ info.createTime }}</span>
      </n-flex>
    </div>
    <div :class="$style['info-btn']">
      <n-button class="!w-full" type="primary" text @click="toDetail"> 查看报名信息 </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';

defineOptions({ name: 'OrderApplicationInfo' });

const router = useRouter();
const { query } = useRoute();

const props = defineProps({
  info: {
    type: Object,
    default: () => ({
      id: '',
      userName: '',
      phone: '',
      workUnit: '',
      createTime: '',
    }),
  },
});

function toDetail() {
  router.push({
    name: 'applicationDetail',
    query: {
      id: query.id,
      backName: 'order',
    },
  });
}
</script>

<style module lang="scss">
.application-info {
  @apply bg-white rounded-[8px];
  .info-main {
    @apply p-[20px] text-[14px] text-[#333];
  }
  .info-title {
    @apply text-[16px] text-[#000] mb-[15px];
    line-height: 1.5;
  }

  .info-item {
    @apply mb-[10px];
  }
  .info-label {
    @apply text-[#636C75];
    white-space: nowrap;
  }

  .info-btn {
    @apply py-[14px];
    border-top: 1px solid #ebebeb;
  }
}
</style>

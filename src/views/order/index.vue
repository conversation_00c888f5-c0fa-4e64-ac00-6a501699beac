<template>
  <div :class="$style['order-wrap']">
    <OrderInfo class="mb-[10px]" :order="orderInfo" />
    <ApplicationInfo :info="applicationInfo" />
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, ref } from 'vue';
import OrderInfo from './comp/orderInfo.vue';
import ApplicationInfo from './comp/applicationInfo.vue';
import { getApplicationDetail } from './fetch';
import { PickApplication, PickOrder } from './types';
import { useRoute } from 'vue-router';
import { sleep } from '@/utils/time';

defineOptions({ name: 'OrderPage' });

const { query } = useRoute();

const orderInfo = ref<PickOrder>({
  qrcodeAddress: '',
  signCode: '',
  status: '',
});
const applicationInfo = ref<PickApplication>({
  id: '',
  userName: '',
  phone: '',
  workUnit: '',
  createTime: '',
});

let getDetail = () => {
  if (!query.id) return;
  getApplicationDetail({
    meetingUserId: query.id,
  })
    .then(async ({ data }) => {
      const { id, userName, phone, workUnit, createTime, qrcodeAddress, signCode, status } = data;
      orderInfo.value = {
        qrcodeAddress,
        signCode,
        status,
      };
      applicationInfo.value = {
        id,
        userName,
        phone,
        workUnit,
        createTime,
      };
    })
    .finally(async () => {
      if (+orderInfo.value.status < 3) {
        await sleep(1000);
        getDetail();
      }
    });
};

getDetail();

onBeforeUnmount(() => {
  getDetail = () => {};
});
</script>

<style module lang="scss">
.order-wrap {
  @apply w-full px-[16px] pb-[28px] box-border bg-[#F4F4F4] overflow-y-auto;
  background: linear-gradient(180deg, #008b84 1%, #f4f4f4 30%);
}
</style>

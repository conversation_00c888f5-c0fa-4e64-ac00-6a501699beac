<template>
  <n-button text @click="handleLogout">退出登录</n-button>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/store/auth';
import { $dialog } from '@/common/shareContext';

const auth = useAuthStore();

function handleLogout() {
  $dialog.info({
    title: '提示',
    content: '确定退出登录吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      auth.logout();
    },
  });
}

defineOptions({ name: 'LogoutIndex' });
</script>

<style scoped lang="scss"></style>

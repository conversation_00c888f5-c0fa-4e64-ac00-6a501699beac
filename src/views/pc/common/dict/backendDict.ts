import { api } from '@/api';
import { IDict } from '@/types';
import { $http } from '@tanzerfe/http';

/**
 * 后端接口字典 API
 */

const cache = new Map();
/**
 * 接口
 */
export const backendDict = {
  /**
   * @param type 字典类型，如传 tzlx
   */
  getSysDictByType(type: string) {
    const url = api.getUrl(api.type.dcs, api.name.dcs.queryDictDataByType, { type });
    return $http.get<IDict[]>(url);
  },
};

/**
 * 清缓存
 */
export function clearBackendDictCache() {
  cache.clear();
}

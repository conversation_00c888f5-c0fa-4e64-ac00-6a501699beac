/**
 * 组织机构树
 */
export interface IOrgTree {
  /**
   * 节点属性
   */
  attributes?: { [key: string]: any };
  /**
   * 节点是否被选中 true false
   * 点是否被选中
   */
  checked?: boolean;
  /**
   * 节点的子节点
   */
  children?: ChildElement[];
  /**
   * 是否有子节点
   */
  hasChildren?: boolean;
  /**
   * 是否有父节点
   */
  hasParent?: boolean;
  /**
   * 节点ID
   * 主键id
   */
  id?: string;
  /**
   * 层级
   */
  level?: number;
  /**
   * 等级code
   */
  levelCode?: string;
  /**
   * 机构类型 ， ,0 部门 1 业务单位 2 监管单位
   */
  orgType?: string;
  /**
   * 父ID
   */
  parentId?: string;
  /**
   * 节点状态，open closed
   */
  state?: MapObject;
  /**
   * 显示节点文本
   * 节点名称
   */
  text?: string;
  /**
   * 树名
   */
  treeName?: string;
  /**
   * 节点类型
   */
  type?: string;
  /**
   * 节点id
   */
  typeId?: string;
  /**
   * 是否单位，0 不是， 1是
   */
  unitStatus?: string;
  [property: string]: any;
}

/**
 * Tree
 */
export interface ChildElement {
  /**
   * 节点属性
   */
  attributes?: { [key: string]: any };
  /**
   * 节点是否被选中 true false
   * 点是否被选中
   */
  checked?: boolean;
  /**
   * 节点的子节点
   */
  children?: ChildElement[];
  /**
   * 是否有子节点
   */
  hasChildren?: boolean;
  /**
   * 是否有父节点
   */
  hasParent?: boolean;
  /**
   * 节点ID
   * 主键id
   */
  id?: string;
  /**
   * 层级
   */
  level?: number;
  /**
   * 等级code
   */
  levelCode?: string;
  /**
   * 机构类型 ， ,0 部门 1 业务单位 2 监管单位
   */
  orgType?: string;
  /**
   * 父ID
   */
  parentId?: string;
  /**
   * 节点状态，open closed
   */
  state?: MapObject;
  /**
   * 显示节点文本
   * 节点名称
   */
  text?: string;
  /**
   * 树名
   */
  treeName?: string;
  /**
   * 节点类型
   */
  type?: string;
  /**
   * 节点id
   */
  typeId?: string;
  /**
   * 是否单位，0 不是， 1是
   */
  unitStatus?: string;
  [property: string]: any;
}

/**
 * 节点状态，open closed
 *
 * MapObject
 */
export interface MapObject {
  key?: { [key: string]: any };
  [property: string]: any;
}

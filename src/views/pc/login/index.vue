<template>
  <div :class="$style['page-login']">
    <PcLogo :class="$style.logo" />
    <div class="com-g-row-a1 relative" :class="$style['login-form']">
      <div :class="$style.title">用户登录</div>
      <LoginForm />
    </div>
  </div>
</template>

<script lang="ts" setup>
import LoginForm from './LoginForm.vue';
import PcLogo from '@/views/pc/logo/index.vue';

defineOptions({ name: 'PcLoginIndex' });
</script>

<style module lang="scss">
.page-login {
  position: relative;
  overflow: hidden;
  height: 100vh;
  min-width: 1600px;
  background: url('./assets/bg.png') no-repeat center;
  background-size: 100%;

  .login-form {
    width: 422px;
    height: 420px;
    position: absolute;
    right: 208px;
    top: 50%;
    padding-top: 40px;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.4);
    border-radius: 8px;

    .title {
      font-size: 32px;
      font-weight: bolder;
      margin: 0 auto 36px;
    }
  }

  .logo {
    position: absolute;
    top: 36px;
    left: 40px;
    transform: scale(0.8333);
  }
}
</style>

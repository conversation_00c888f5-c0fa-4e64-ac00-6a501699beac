<template>
  <div :class="$style['form-wrap']">
    <n-form
      ref="formRef"
      class="com-autofill-none-light"
      :model="formVal"
      :rules="rules"
      label-placement="left"
      label-width="auto"
      require-mark-placement="right-hanging"
      size="large"
    >
      <n-form-item label="" path="phoneNumber" ref="phoneRef">
        <n-input v-model:value="formVal.phoneNumber" placeholder="请输入手机号" clearable> </n-input>
      </n-form-item>
      <n-form-item label="" path="pwd" style="margin-top: 15px">
        <n-input v-model:value="formVal.pwd" placeholder="请输入密码" type="password">
          <!--          <template #suffix>-->
          <!--            <div class="text-[#008982]" @click="sendCaptchaSms">{{ captchaLabel }}</div>-->
          <!--          </template>-->
        </n-input>
      </n-form-item>

      <div :class="$style['btn-login']">
        <n-button color="#0c87e5" :loading="loading" @click="handleValidateSubmit" size="large" class="!text-white">
          立即登录
        </n-button>
      </div>
    </n-form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { FormInst, FormRules } from 'naive-ui';
import { useAutoLoading } from '@/common/hooks/useAutoLoading.ts';
import { useAuthPcStore as useAuthStore } from '@/store/auth-pc';
import { useCaptcha } from '@/views/login/useCaptcha.ts';
import { isMobilePhone } from '@/utils/validate.ts';
import { encryptMd5 } from '@/utils/crypto.ts';

const formVal = ref({
  phoneNumber: null,
  pwd: null,
  // verifyCode: null,
});
const rules: FormRules = {
  phoneNumber: {
    required: true,
    trigger: ['blur'],
    validator(rule, val) {
      if (!val) return new Error('请输入手机号码');
      return isMobilePhone(val) ? true : new Error('请输入正确的手机号码');
    },
  },
  pwd: {
    required: true,
    trigger: ['blur'],
    message: '请输入密码',
  },
  // verifyCode: {
  //   required: true,
  //   trigger: ['blur'],
  //   message: '请输入验证码',
  // },
};
const authStore = useAuthStore();
const formRef = ref<FormInst | null>(null);
const [loading, submit] = useAutoLoading();
// const { phoneRef, captchaTimer, captchaLabel } = useCaptcha();

// function sendCaptchaSms() {
//   captchaTimer.start(formVal.value.phoneNumber);
// }

function handleValidateSubmit() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      if (loading.value) return;
      const params = {
        loginName: formVal.value.phoneNumber,
        password: encryptMd5(`${formVal.value.pwd}true`),
      };
      submit(authStore.login(params)).then(() => {});
    }
  });
}

defineOptions({ name: 'PcLoginFormNew' });
</script>

<style module lang="scss">
.form-wrap {
  padding: 0 32px;

  :global(.n-input) {
    border-radius: 10px;
  }

  :global(.n-input .n-input__input-el) {
    height: 56px;
  }

  .btn-login {
    margin-top: 16px;

    > button {
      width: 100%;
      height: 56px;
      border-radius: 10px;
    }
  }
}
</style>

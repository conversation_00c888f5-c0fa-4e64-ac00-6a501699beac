<template>
  <div :class="$style.PcLogo">
    <img src="./assets/logo.svg" alt="" class="w-[72px] select-none" />
    <img src="./assets/title.svg" alt="公共安全科学技术学会" class="w-[218px]" />
  </div>
</template>

<script setup lang="ts">
defineOptions({ name: '<PERSON>cL<PERSON>' });
</script>

<style module lang="scss">
.PcLogo {
  width: 310px;
  display: flex;
  justify-content: space-between;
}
</style>

import { $toast } from '@/common/shareContext';
import { Env } from '@/views/pay/service/Env.ts';
import { PayService } from '@/views/pay/service/PayService.ts';
import router from '@/router/h5';
import { getOrderStatus } from '@/views/pay/fetchData.ts';

export class PayComp {
  async pullPay(payChannel: string, order: string, userId: string) {
    if (payChannel === 'wxpay') {
      if (!Env.isWx) {
        alert('请在微信浏览器中打开');
        return;
      }

      await this.checkUnPay(order, userId);
      PayService.openWxSnsURL({ payOrder: order, userId });
    }

    if (payChannel === 'alipay') {
      // $toast.warning('正在拉起支付: ' + payChannel);
      await this.checkUnPay(order, userId);

      await router.push({
        name: 'payCheck',
        query: { payChannel: payChannel, orderCode: order, userId, t: +new Date() },
      });
      PayService.pullAliPay({ payOrder: order, userId });
    }
  }

  /**
   * 是否是未支付
   * @param order
   * @param userId
   */
  async checkUnPay(order: string, userId: string) {
    return new Promise((resolve) => {
      getOrderStatus(order).then((res) => {
        const status = res.data;

        if (status === '1') {
          router.replace({ name: 'order', query: { id: userId } });
        } else if (status === '0') {
          resolve('0'); // 仅待支付才允许拉起支付
        } else {
          $toast.error('订单异常，请刷新页面重试');
        }
      });
    });
  }
}

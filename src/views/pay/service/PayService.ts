import { prepay } from '@/views/pay/fetchData.ts';

export class PayService {
  static Appid = 'wx1b2a052405cf5f2d';

  static openWxSnsURL(data: { payOrder: string; userId: string }) {
    const snsapi_uri = 'https://open.weixin.qq.com/connect/oauth2/authorize';
    const redirect_uri = `${window.$SYS_CFG.payRedirectURL}#/pay-jump?payChannel=wxpay&userId=${data.userId}&orderCode=${data.payOrder}&t=${+new Date()}`;

    // https://open.weixin.qq.com/connect/oauth2/authorize?appid=APPID&redirect_uri=REDIRECT_URI&response_type=code&scope=SCOPE&state=STATE#wechat_redirect
    window.location.href = `${snsapi_uri}?appid=${this.Appid}&redirect_uri=${encodeURIComponent(redirect_uri)}&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect`;
  }

  static pullWxPay(data: any, cb: any) {
    WeixinJSBridge.invoke(
      'getBrandWCPayRequest',
      {
        appId: data.appId, // 公众号ID，由商户传入
        timeStamp: data.timeStamp, // 时间戳，自1970年以来的秒数
        nonceStr: data.nonceStr, // 随机串
        package: data.packageVal,
        signType: data.signType, // 微信签名方式
        paySign: data.paySign, // 微信签名
      },
      function (res: any) {
        cb(res.err_msg || res.errMsg || 'error');

        // 返回值	描述
        // get_brand_wcpay_request:ok	支付成功
        // get_brand_wcpay_request:cancel	支付过程中用户取消
        // get_brand_wcpay_request:fail	支付失败
        // if (res.err_msg == 'get_brand_wcpay_request:ok') {
        //   // 使用以上方式判断前端返回,微信团队郑重提示：
        //   // res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
        // }
      }
    );
  }

  static pullAliPay(data: { payOrder: string; userId: string }) {
    const redirect_uri = `${window.$SYS_CFG.payRedirectURL}#/pay-check?payChannel=alipay&userId=${data.userId}&orderCode=${data.payOrder}&t=${+new Date()}`;

    prepay({
      openId: 'alipay',
      outTradeNo: data.payOrder,
      payType: '1',
      returnUrl: redirect_uri,
    }).then((res: any) => {
      const div = document.createElement('div');
      div.style.display = 'none';
      div.innerHTML = res.data;
      document.body.appendChild(div);
      document.forms[0].submit();
      // window.open(
      //   `https://qd.publicsafety.org.cn/api/v1/sign-up-service/pay/aliWapPayTest?outTradeNo=${data.payOrder}&payType=1&returnUrl=${encodeURIComponent(redirect_uri)}`,
      //   '_blank'
      // );
    });
  }
}

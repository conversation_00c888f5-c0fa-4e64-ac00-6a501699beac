<template>
  <div :class="$style['pay-wrap']">
    <div :class="$style['pay-info']">
      <p class="font-bold text-[24px]">报名信息</p>
      <p>报名人姓名：{{ detail.userName }}</p>
      <p>报名人手机号：{{ detail.userPhone }}</p>
      <p>报名人单位名称：{{ detail.companyName }}</p>

      <n-button
        v-if="id"
        class="!absolute top-[18px] right-[10px] !text-[18px]"
        round
        color="#008982"
        @click="handleChangeInfo"
      >
        <template #icon><FeEdit /></template>
        修改信息
      </n-button>
    </div>

    <div>
      <p :class="$style['pay-timer']">
        支付剩余时间：<span class="font-bold">{{ timerStr }}</span>
      </p>
      <p :class="$style['pay-amount']">
        <span class="text-[24px]">￥</span>
        <span class="text-[40px]">{{ detail.payRecord?.payMoney || '--' }}</span>
      </p>
    </div>

    <div :class="$style['pay-channel']">
      <div
        :class="$style['pay-channel-item']"
        class="bg-white dark:bg-skin-bg3"
        v-for="item of channelList"
        :key="item.value"
        @click="handleCheckPayChannel(item.value)"
      >
        <n-flex align="center">
          <img :src="item.icon" class="w-[24px] h-[24px]" alt="" />
          <span>{{ item.label }}</span>
        </n-flex>
        <CheckBox :checked="payChannel === item.value" />
      </div>
    </div>

    <div :class="$style['pay-btn']">
      <n-button class="w-full" type="primary" @click="handlePay" :disabled="!id || !orderId">确认支付</n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeUnmount, ref } from 'vue';
import CheckBox from './CheckBox.vue';
import wxIcon from './assets/wxpay.png';
import zfbIcon from './assets/alipay.png';
import { PayComp } from '@/views/pay/PayComp.ts';
import { useRoute, useRouter } from 'vue-router';
import { queryPayRecord } from '@/views/pay/fetchData.ts';
import { IQueryPayRecord } from '@/views/pay/type.ts';
import { trimObjEmpty } from '@/utils/obj.ts';
import { Timer } from 'easytimer.js';
import { sleep } from '@/utils/time.ts';
import { $toast } from '@/common/shareContext';
import { FeEdit } from '@kalimahapps/vue-icons';

const router = useRouter();
const payComp = new PayComp();
const route = useRoute();
const id = computed(() => <string>route.query.id || ''); // user id

const channelList = [
  { value: 'wxpay', label: '微信支付', icon: wxIcon },
  { value: 'alipay', label: '支付宝支付', icon: zfbIcon },
];
const payChannel = ref('wxpay');

const detail = ref<Partial<IQueryPayRecord>>({});
const orderId = computed(() => detail.value.payRecord?.outTradeNo || '');
const timer = new Timer();
const timerStr = ref('--:--');

function handleChangeInfo() {
  router.push({ name: 'applicationForm', query: { id: id.value, t: +new Date() } });
}

function handleCheckPayChannel(val: string) {
  payChannel.value = val;
}

function handlePay() {
  payComp.pullPay(payChannel.value, orderId.value, id.value);
}

function getDetail() {
  if (id.value) {
    queryPayRecord(id.value).then((res) => {
      const payEndTime = res.data.payRecord?.payEndTime;
      detail.value = trimObjEmpty(res.data || {});

      if (payEndTime) {
        const s = Math.max(0, (payEndTime - +new Date()) / 1000);
        timer.start({ countdown: true, startValues: { seconds: s } });
      }
    });
  }
}

function init() {
  getDetail();

  // Listener
  timer.addEventListener('secondsUpdated', function () {
    const str = timer.getTimeValues().toString();
    timerStr.value = str.startsWith('00:') ? str.substring(3) : str; // 去掉前面 00:
  });
  timer.addEventListener('targetAchieved', async function () {
    // 订单号过期 支付按钮置灰
    detail.value.payRecord = Object.assign({}, detail.value.payRecord, { outTradeNo: '' });
    $toast.warning('支付超时');
    // 计时结束 延迟1秒调用接口重新生成订单
    await sleep(1000);
    getDetail();
  });
}

// created
init();

onBeforeUnmount(() => {
  timer?.stop();
});

defineOptions({ name: 'PayPay' });
</script>

<style module lang="scss">
.pay-wrap {
  padding-top: 20px;
  padding-bottom: 20px;

  .pay-info {
    position: relative;
    width: 100%;
    height: 132px;
    background: url('./assets/b1.png') center center no-repeat;
    background-size: 100%;
    padding: 10px;
    color: #fff;
    line-height: 1.65;
  }

  .pay-timer {
    font-size: 16px;
    color: var(--skin-t4);
    text-align: center;
    padding: 26px 0 12px;
  }

  .pay-amount {
    text-align: center;
    font-weight: bold;
  }

  .pay-channel {
    margin-top: 50px;
    font-size: 18px;
    font-weight: bold;
  }

  .pay-channel-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 62px;
    border-radius: 8px;
    padding: 0 20px;
    margin-bottom: 10px;
  }

  .pay-btn {
    position: fixed;
    bottom: 24px;
    width: calc(100% - 48px);
    > button {
      width: 100%;
      height: 48px;
      border-radius: 8px;
      font-size: 20px;
    }
  }
}
</style>

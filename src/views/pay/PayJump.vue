<template>
  <div></div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { PayService } from '@/views/pay/service/PayService.ts';
import { cancelPrepay, getOpenidByCode, prepay } from '@/views/pay/fetchData.ts';
import router from '@/router/h5';
import { useRoute } from 'vue-router';
import { $toast } from '@/common/shareContext';

const route = useRoute();

function handleJump() {
  const code = window.$_getUrlParams('code');
  const orderCode = window.$_getUrlParams('orderCode');

  if (code) {
    getOpenidByCode(code).then((res) => {
      const openId = res.data;

      if (openId) {
        prepay({
          openId,
          outTradeNo: orderCode,
          payType: '2',
        }).then((rp: any) => {
          const id = route.query.userId;
          const back = () => {
            $toast.error('支付失败');
            router.replace({ name: 'payPay', query: { id } }).then(() => {
              window.location.reload();
            });
          };

          if (rp.data) {
            PayService.pullWxPay(rp.data || {}, (msg: any) => {
              if (msg === 'get_brand_wcpay_request:cancel' || msg === 'get_brand_wcpay_request:fail') {
                cancelPrepay({ outTradeNo: orderCode }).finally(() => {
                  back();
                });
              } else {
                router.push({ name: 'order', query: { id } });
              }
            });
          } else {
            alert('[prepay] 接口返回异常');
            back();
          }
        });
      }
    });
  }
}

onMounted(() => {
  setTimeout(() => {
    handleJump();
  }, 200);
});
defineOptions({ name: 'PayJump' });
</script>

<style module lang="scss"></style>

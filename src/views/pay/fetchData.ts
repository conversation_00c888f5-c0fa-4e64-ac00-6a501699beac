import { api } from '@/api';
import { $http } from '@tanzerfe/http';
import { IObj } from '@/types';
import { IQueryPayRecord } from '@/views/pay/type.ts';

export function getOpenidByCode(code: string) {
  const url = api.getUrl(api.type.sus, api.name.sus.getOpenidByCode, { code });

  return $http.get(url);
}

export function prepay(query: IObj<any>) {
  const url = api.getUrl(api.type.sus, api.name.sus.prepay, query);

  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

export function cancelPrepay(query: IObj<any>) {
  const url = api.getUrl(api.type.sus, api.name.sus.cancelPrepay, query);

  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

export function queryPayRecord(id: string) {
  const url = api.getUrl(api.type.sus, api.name.sus.queryPayRecord, { meetingUserId: id });

  return $http.get<IQueryPayRecord>(url, { data: { _cfg: { showTip: true } } });
}

export function getOrderStatus(id: string) {
  const url = api.getUrl(api.type.sus, api.name.sus.getOrdeStatus, { outTradeNo: id });

  return $http.get(url, { data: { _cfg: { showTip: true } } });
}

<template>
  <div :class="$style['checked-box']">
    <img v-if="checked" :class="$style['checked']" src="./assets/checked.png" alt="1" />
    <div v-else :class="$style['unchecked']"></div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  checked: {
    type: Boolean,
    default: false,
  },
});

defineOptions({ name: 'CheckBox' });
</script>

<style module lang="scss">
.checked-box {
  .checked {
    width: 22px;
    height: 22px;
  }

  .unchecked {
    width: 22px;
    height: 22px;
    background: #ffffff;
    border: 1px solid #d7dfe6;
    border-radius: 22px;
  }
}
</style>

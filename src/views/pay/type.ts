export interface IQueryPayRecord {
  payRecord: {
    outTradeNo: string;
    transactionMeetingUserId: string;
    transactionUserName: string;
    payType: string;
    payMoney: number;
    addSub: string;
    payStatus: string;
    payNo: string;
    refundPayNo: string;
    payTime: string;
    createTime: string;
    updateTime: string;
    payEndTime: number;
    payWxOpenid: string;
    buyerLogonId: string;
  };
  userPhone: string;
  companyName: string;
  userName: string;
}

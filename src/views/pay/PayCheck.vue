<template>
  <div :class="$style['pay-check-wrap']" :style="`color: ${payStatusInfo.color}`">
    <div>{{ payStatusInfo.str }}</div>

    <div :class="$style['pay-btn']" v-if="payStatus === '1'">
      <n-button class="w-full" type="primary" @click="handleToOrder" ghost>查看</n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { sleep } from '@/utils/time.ts';
import { getOrderStatus } from '@/views/pay/fetchData.ts';
import { $toast } from '@/common/shareContext';

const router = useRouter();
let timer: any;

type IStatus = '-1' | '0' | '1' | '2' | '9';

const payStatus = ref<IStatus>('-1'); // [-1: 查询中, 9: 订单异常], 0: 待支付, 1: 支付成功, 2: 支付失败

const statusMapping: Record<IStatus, { color: string; str: string }> = {
  '-1': { color: 'rgba(255, 102, 0, 0.8)', str: '查询中...' }, // 查询中
  '0': { color: 'rgba(255, 102, 0, 0.8)', str: '待支付' }, // 待支付
  '1': { color: 'rgba(0, 128, 0, 0.8)', str: '支付成功' }, // 支付成功
  '2': { color: 'rgba(249, 38, 38, 0.8)', str: '支付失败' }, // 支付失败
  '9': { color: 'rgba(249, 38, 38, 0.8)', str: '订单异常' }, // 订单异常
};

const payStatusInfo = computed(() => statusMapping[payStatus.value]);
const checkLoop = ref(0);

async function queryOrderStatus() {
  const { payChannel, out_trade_no, orderCode } = router.currentRoute.value.query || {};
  const order = <string>(orderCode || out_trade_no || '');

  if (payChannel === 'alipay' && order) {
    payStatus.value = '-1';
    await sleep(500);
    await getOrderStatus(order)
      .then(async (res) => {
        payStatus.value = <IStatus>(res.data + '') || '9';

        await sleep(500);
        if (payStatus.value === '1') {
          handleToOrder();
        } else {
          if (checkLoop.value > 2) {
            handleToPay();
          }
        }
      })
      .catch(() => {
        payStatus.value = '9';
      });
  }
}

function startCheck() {
  if (timer) return;
  timer = setInterval(async () => {
    checkLoop.value++;
    await queryOrderStatus();
    if (checkLoop.value > 10 && (payStatus.value === '2' || payStatus.value === '9')) {
      stopCheck();
    }
  }, 2000);
}

function stopCheck() {
  clearInterval(timer);
  checkLoop.value = 0;
  timer = undefined;
}

function handleVisibilitychange() {
  if (document.hidden) {
    stopCheck();
  } else {
    startCheck();
  }
}

function handleToOrder() {
  router.replace({ name: 'order', query: { id: router.currentRoute.value.query.userId } });
}
function handleToPay() {
  $toast.error('支付失败');
  router.replace({ name: 'payPay', query: { id: router.currentRoute.value.query.userId } });
}

onMounted(() => {
  startCheck();
  // 检查浏览器是否支持 Page Visibility API
  if (typeof document.hidden !== 'undefined') {
    document.addEventListener('visibilitychange', handleVisibilitychange);
  }
});

onBeforeUnmount(() => {
  stopCheck();
  document.removeEventListener('visibilitychange', handleVisibilitychange);
});

watch(
  () => router.currentRoute.value.query,
  () => {
    queryOrderStatus();
  },
  { immediate: true }
);

defineOptions({ name: 'PayCheck' });
</script>

<style module lang="scss">
.pay-check-wrap {
  display: grid;
  place-content: center;
  place-items: center;
  font-size: 30px;
  color: rgba(0, 128, 0, 0.8);

  .pay-btn {
    position: fixed;
    bottom: 24px;
    width: calc(100% - 48px);
    > button {
      width: 100%;
      height: 48px;
      border-radius: 8px;
      font-size: 20px;
    }
  }
}
</style>

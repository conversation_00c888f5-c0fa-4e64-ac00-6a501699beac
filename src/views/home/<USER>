<template>
  <div></div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { meetingUserInfo } from '@/store/auth/type.ts';
import { useAuthStore } from '@/store/auth';

const router = useRouter();
const userStore = useAuthStore();

function jumpCom() {
  const { id = '', status = '', userType } = userStore.userInfo.meetingUserInfo as meetingUserInfo;
  if (userType !== '0') {
    router.replace({ name: 'workbenches' });
  } else {
    // 0=未报名，1已报名未付款，2=已支付
    if (!status || status === '0') router.replace({ name: 'applicationForm' });
    if (status === '1') router.replace({ name: 'payPay', query: { id } });
    if (status === '2') router.replace({ name: 'order', query: { id } });
  }
}

jumpCom();

defineOptions({ name: 'HomeIndex' });
</script>

<style scoped lang="scss"></style>

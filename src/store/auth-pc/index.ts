import pkg from '../../../package.json';
import type { IAuthState } from './type';
import { defineStore } from 'pinia';
import { IObj } from '@/types';
import router from '@/router/pc-uc';
import { postLogin } from '@/store/auth/fetchData.ts';

export const useAuthPcStore = defineStore(`${pkg.name}-pc-store`, {
  persist: true,
  state: (): IAuthState => ({
    userInfo: {
      token: '',
    },
  }),
  getters: {
    // 登录状态
    isLogin(state) {
      return Boolean(state.userInfo.token);
    },
    isSuperAdmin(state) {
      return Boolean(state.userInfo.manageTag == '1');
    },
  },
  actions: {
    async login(data: IObj<any>) {
      const res = await postLogin(data);

      this.userInfo = res.data;
      await router.push({ name: 'pcUcIndex' });
    },
    async logout(opt?: any) {
      this.reset();
      await router.push({ name: 'pcLogin' });
    },
    reset() {
      this.$reset();
    },
  },
});

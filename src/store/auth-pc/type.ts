export interface IAuthState {
  /** 用户信息 */
  userInfo: PmTUserVo;
}

/**
 * 数据
 * 返回数据
 *
 * PmTUserVo
 */
export interface PmTUserVo {
  /**
   * 用户单位关联的租户内部集团单位id，拿鞍钢举例： 承租方 相关方的人进来这个值是鞍钢集团，鞍钢的人进来这个也是鞍钢集团
   */
  associationTopUnitId?: string;
  /**
   * 市级code
   */
  cityCode?: string;
  /**
   * 登录终端：浏览器 WEB，手机端：APP ；公众号：wxApp
   */
  client?: string;
  /**
   * 区县code
   */
  countyCode?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * unitId对应的电子档案单位id
   */
  erecordUnitId?: string;
  /**
   * 过期时间
   */
  expireTime?: number;
  /**
   * 用户id
   */
  id?: string;
  /**
   * 登录名
   */
  loginName?: string;
  /**
   * 管理员角色类型 '1'为超管
   */
  manageTag?: string;
  /**
   * 公众号openId
   */
  openId?: string;
  /**
   * 机构编码
   */
  orgCode?: string;
  /**
   * 机构名称
   */
  orgName?: string;
  /**
   * 机构来源,1内部 2 相关方 3：承租方
   */
  orgRes?: string;
  /**
   * 部门类型
   */
  orgType?: string;
  /**
   * 密码
   */
  password?: string;
  /**
   * 用户头像路径
   */
  photoUrl?: string;
  /**
   * 岗位id
   */
  postId?: string;
  /**
   * 岗位名称
   */
  postName?: string;
  /**
   * 省级code
   */
  provinceCode?: string;
  resourceVoList?: RoleResourceVo[];
  roleIds?: string;
  /**
   * 角色标签
   */
  roleMarks?: string;
  /**
   * 服务单位信息
   */
  serverOrgList?: PmUserSimpleVo[];
  /**
   * 服务单位，相关方单位 关联表的createOrgCode
   */
  serverUnitId?: string;
  /**
   * 服务单位，相关方单位 关联表的createOrgName
   */
  serverUnitName?: string;
  /**
   * 服务单位机构类型, 1 业务单位 2 监管单位
   */
  serverUnitOrgType?: string;
  /**
   * 系统编码
   */
  sysCode?: string;
  /**
   * 用户token
   */
  token?: string;
  /**
   * 集团单位id
   */
  topUnitId?: string;
  /**
   * 缓存uid
   */
  uid?: string;
  /**
   * 单位id
   */
  unitId?: string;
  /**
   * 单位名称
   */
  unitName?: string;
  /**
   * 单位机构类型, 1 业务单位 2 监管单位
   */
  unitOrgType?: string;
  /**
   * 用户名
   */
  userName?: string;
  /**
   * 0：男，1：女，2：其他
   * 0：男，1：女
   */
  userSex?: string;
  /**
   * 手机号
   */
  userTelphone?: string;
  userType?: string;
  /**
   * 租户id
   */
  zhId?: string;
  /**
   * 租户logo
   */
  zhLogo?: string;
  /**
   * 租户logoUrl
   */
  zhLogoUrl?: string;
  /**
   * 租户名称
   */
  zhName?: string;
  /**
   * 租户平台地址
   */
  zhPlatformUrl?: string;
  [property: string]: any;
}

/**
 * RoleResourceVo
 */
export interface RoleResourceVo {
  /**
   * 子菜单
   */
  childrens?: RoleResourceVo[];
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人id
   */
  createUserId?: string;
  /**
   * 数据来源
   */
  dataRes?: string;
  /**
   * 删除标志（0正常 1删除）
   */
  delFlag?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 是否为管理员资源：0是1否
   */
  isAdmin?: string;
  /**
   * 是否已选择:0是1否
   */
  isChoose?: string;
  /**
   * 是否是默认 0是 1否
   */
  isDefault?: string;
  /**
   * 资源状态（0:可见，1不可见）
   */
  isVisibled?: string;
  /**
   * 父资源ID
   */
  parentId?: string;
  /**
   * 资源别名
   */
  resAlias?: string;
  /**
   * 资源图标
   */
  resIcon?: string;
  /**
   * 资源标识
   */
  resIdent?: string;
  /**
   * 资源名称
   */
  resName?: string;
  /**
   * 排序
   */
  resOrder?: number;
  /**
   * 请求方式 get,post,put,delete，暂留
   */
  resRequestType?: string;
  /**
   * 资源类型（0:目录，1：菜单，2：按钮）
   * 资源类型 0:目录，1：菜单，2：按钮
   */
  resType?: string;
  /**
   * 资源地址
   */
  resUrl?: string;
  /**
   * 应用系统code
   */
  sysCode?: string;
  /**
   * 修改时间
   */
  updateTime?: string;
  /**
   * 修改人
   */
  updateUserId?: string;
  /**
   * 是租户id,菜单定制的需要填,默认值：default
   */
  zhId?: string;
  [property: string]: any;
}

/**
 * PmUserSimpleVo
 */
export interface PmUserSimpleVo {
  orgCode?: string;
  orgName?: string;
  /**
   * 服务单位机构类型, 1 业务单位 2 监管单位
   */
  orgType?: string;
  [property: string]: any;
}

import { ILoginRes } from '@/store/auth/type.ts';
import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

export function postLogin(data: IObj<any>) {
  const url = api.getUrl(api.type.sus, api.name.sus.userLogin);

  return $http.post<ILoginRes>(url, {
    data: {
      _cfg: {
        noAuth: true,
        showTip: true,
      },
      ...data,
    },
  });
}

import { ILoginRes } from '@/store/auth/type.ts';
import { IObj } from '@/types';
import { api } from '@/api';
import { $http } from '@tanzerfe/http';

export function postLogin(data: IObj<any>) {
  const query = { sysCode: 'ggaqxh', client: 'web', ...data };
  const url = api.getUrl(api.type.dcs, api.name.dcs.login, query);

  return $http.post<ILoginRes>(url, {
    data: {
      _cfg: {
        noAuth: true,
        showTip: true,
      },
    },
  });
}

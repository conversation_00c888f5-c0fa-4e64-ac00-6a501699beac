export interface IAuthState {
  /** 用户信息 */
  userInfo: Partial<ILoginRes>;
}

export interface meetingUserInfo {
  acount: string;
  bankAndAccount: string;
  checkTakerMail: string;
  checkTakerMobile: string;
  conferenceName: string;
  corporateName: string;
  createTime: string;
  createUserId: string;
  gender: string;
  id: string;
  invoiceName: string;
  invoiceType: string;
  mail: string;
  meetingId: string;
  meetingVenueId: string;
  meetingVenueName: string;
  orderNo: string;
  phone: string;
  position: string;
  positionRank: string;
  qrcodeAddress: string;
  registeredAddressMobile: string;
  signCode: string;
  signStatus: string;
  signTime: string;
  signUpPayTime: string;
  studentStatus: string;
  ticketDelivery: string;
  userName: string;
  userType: string; // 0参会人员 1管理员 2志愿者
  workUnit: string;
  status: string; // 0=未报名，1已报名未付款，2=已支付，3=已签到
}

/**
 * 登录接口返回的信息
 */
export interface ILoginRes {
  isFlag: boolean;
  token: string;
  meetingUserInfo: meetingUserInfo;
}

import pkg from '../../../package.json';
import { IAuthState, meetingUserInfo } from './type';
import { defineStore } from 'pinia';
import { IObj } from '@/types';
import router from '@/router/h5';
import { postLogin } from '@/store/auth/fetchData.ts';

export const useAuthStore = defineStore(`${pkg.name}-store`, {
  persist: true,
  state: (): IAuthState => ({
    userInfo: {
      token: '',
    },
  }),
  getters: {
    // 登录状态
    isLogin(state) {
      return Boolean(state.userInfo.token);
    },
  },
  actions: {
    async login(data: IObj<any>) {
      const res = await postLogin(data);

      this.userInfo = res.data;

      const { id = '', status = '', userType } = this.userInfo.meetingUserInfo as meetingUserInfo;
      if (userType !== '0') {
        await router.replace({ name: 'workbenches' });
      } else {
        // 0=未报名，1已报名未付款，2=已支付
        if (!status || status === '0') await router.replace({ name: 'applicationForm' });
        if (status === '1') await router.replace({ name: 'payPay', query: { id } });
        if (status === '2' || status === '3') await router.replace({ name: 'order', query: { id } });
      }
    },
    async logout() {
      await router.push({ name: 'login' });
      this.reset();
      // const userId = this.userInfo.meetingUserInfo?.id + '';
      //
      // useMndCtx().loadingBar.start();
      // postLogout(userId).finally(async () => {
      //   try {
      //     await router.push({ name: 'login' });
      //     useMndCtx().loadingBar.finish();
      //   } catch (e) {}
      // });
    },
    refresh(info: meetingUserInfo) {
      this.userInfo.meetingUserInfo = info;
    },
    reset() {
      this.$reset();
    },
  },
});

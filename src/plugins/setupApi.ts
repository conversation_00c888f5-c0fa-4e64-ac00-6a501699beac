import { $Config } from '@tanzerfe/http';
import useToastCtx from '@/common/shareContext/useToastCtx.ts';
import { useAuthStore } from '@/store/auth';
import { useAuthPcStore } from '@/store/auth-pc';

export function setupApi() {
  const store = useAuthStore();
  const pcStore = useAuthPcStore();

  $Config.getToken = () => (window.__XF__PLATFORM_PC ? pcStore.userInfo.token : store.userInfo.token);
  $Config.$toastDark = useToastCtx({ theme: 'dark' });
  $Config.$toastLight = useToastCtx({ theme: 'light' });
}
